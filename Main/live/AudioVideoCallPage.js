import React from 'react';
import {
  Dimensions,
  Animated,
  PanResponder,
  Easing,
  <PERSON><PERSON><PERSON>iew,
  FlatList,
  requireNative<PERSON><PERSON>po<PERSON>,
  find<PERSON><PERSON><PERSON><PERSON><PERSON>, UIManager
} from "react-native";

import {
  Package,
  Device,
  Service,
  Host,
  PackageEvent,
  System,
  DarkMode,
  DeviceEvent,
  API_LEVEL,
  UserExpPlanEvent,
  USER_EXP_PLAN_EVENT_TYPES
} from 'miot';
import { Permissions } from "miot/system/permission";
import {
  NativeModules,
  StatusBar,
  DeviceEventEmitter,
  Platform,
  BackHandler,
  PermissionsAndroid,
  Image,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback
} from 'react-native';
import { MISSCommand, MISSConnectState, MISSError } from "miot/service/miotcamera";
import CameraRenderView, { MISSCodec } from 'miot/ui/CameraRenderView';
import { MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import { Base64 } from '../util/Base64';
import base64js from 'base64-js';
import Orientation from 'react-native-orientation';
import ImageButton from "miot/ui/ImageButton";


import LinearGradient from 'react-native-linear-gradient';
import { SingleChoseDialog, TouchableView } from "miot/ui";
import DeviceOfflineDialog from "../ui/DeviceOfflineDialog";
import NoNetworkDialog from "../ui/NoNetworkDialog";
import PanoramaViewDialog from "../ui/PanoramaViewDialog";
import CommonMsgDialog from "../ui/CommonMsgDialog";
import RPC from '../util/RPC';
import LogUtil from '../util/LogUtil';
import DirectionView, { DirectionViewConstant } from '../ui/DirectionView';
import DirectionHorizontalView from '../ui/DirectionHorizontalView';

import RectAngleView from '../ui/RectAngleView';
import OverAllControlView from "../ui/OverAllControlView";

import { TouchableHighlight } from "react-native";

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import CameraPlayer, { MISSCommand_ECO } from '../util/CameraPlayer';
import StorageKeys from '../StorageKeys';
import API from '../API';

import VersionUtil from '../util/VersionUtil';
import AlbumHelper from "../util/AlbumHelper";

import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import AlarmUtil, { SPEC_PIID_KEY_VOLUME, SPEC_SIID_KEY_SPEAKER } from '../util/AlarmUtil';

import { ChoiceDialog, AbstractDialog } from 'miot/ui/Dialog';
import { InputDialog, MessageDialog } from "mhui-rn";
import SlideGear from "../ui/SlideGear";

import NumberUtil from '../util/NumberUtil';
import TrackUtil from '../util/TrackUtil';
import LoadingView from '../ui/LoadingView';
import Util, { GermanCluster } from "../util2/Util";
import { CldDldTypes } from "../framework/CloudEventLoader";
import { CAMERA_CONTROL_SEPC_PARAMS, DescriptionConstants, devOpen } from '../Constants';
import TrackConnectionHelper from '../util/TrackConnectionHelper';
import { feedbackLogUploaderStatus, fetchLogUploaderStatus } from '../util/LogUploader';

import MHLottieVoiceButton, { MHLottieVoiceBtnDisplayState } from '../ui/animation/lottie-view/MHLottieVoiceButton';
import MHLottieSnapButton, { MHLottieSnapBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapButton';
import MHLottieRecordButton, { MHLottieRecordBtnDisplayState } from '../ui/animation/lottie-view/MHLottieRecordButton';
import MHLottieControlButton, {
  MHLottieControlBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieControlButton';

import MHLottieSleepToolButton, {
  MHLottieSleepToolBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieSleepToolButton';
import MHLottieAudioToolButton, {
  MHLottieAudioToolBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieAudioToolButton';
import MHLottieQulityToolButton, {
  MHLottieQulityToolBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieQulityToolButton';
import MHLottieFullScreenToolButton from '../ui/animation/lottie-view/MHLottieFullScreenToolButton';

import MHLottieSnapLandscapeButton, {
  MHLottieSnapLandscapeBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieSnapLandscapeButton';
import MHLottieRecordLandscapeButton, {
  MHLottieRecordLandscapeBtnDisplayState
} from '../ui/animation/lottie-view/MHLottieRecordLandscapeButton';
import MHLottieVoiceLandscapeButton from '../ui/animation/lottie-view/MHLottieVoiceLandscapeButton';
import OfflineHelper from '../util/OfflineHelper';
import StatusBarUtil from '../util/StatusBarUtil';
import PinCodeUtil from '../util/PinCodeUtil';
import { getStack, isStartUpPush } from '../index';
import NetInfo from '@react-native-community/netinfo';
import DldMgr from '../framework/DldMgr';
import VipUtil from '../util/VipUtil';
import SpecUtil from '../util/SpecUtil';
import PrivacySDKUtil from '../util/PrivacySDKUtil';
import { removeDarkListener } from '../setting/SettingStyles';
// import { turn } from '../../../../bin/local-cli/bundle/bundleCommandLineArgs';
import dayjs from 'dayjs';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import { PAD_SCROLL_STRATEGY } from 'miot/Host';
import ImageTextButton from "../ui/ImageTextButton";
import MHLottieRecordToolButton from "../ui/animation/lottie-view/MHLottieRecordToolButton";
import MHLottieSnapToolButton from "../ui/animation/lottie-view/MHLottieSnapToolButton";
import iconBack from "../../Resources/Images/icon_back_black_nor_dark.png";
import iconBackPre from "../../Resources/Images/icon_back_black_nor_dark.png";
import MHCameraCaptureView from "./MHCameraCaptureView";
import PropTypes from "prop-types";
import DateFormater from "../util2/DateFormater";
import I18n from "miot/resources/Strings";
import AlarmUtilV2, { PIID_CALL_VOLUME, SIID_CALL_VOLUME } from "../util/AlarmUtilV2";
import ParsedText from "react-native-parsed-text";
import { handlerOnceTap } from "../util/HandlerOnceTap";

// import NetInfo from "@react-native-community/netinfo";

const kBpsDataReceiveCallbackName = "bpsDataReceiveCallback";
const kRecordTimeCallbackName = "recordTimeCallback";
const kRDTDataReceiveCallBackName = 'rdtDataReceiveCallBack';
const KFirstFrameOverExposedResultCallBackName = 'overExposedResultCallBack';
const kScreenHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
// const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
const navigationBarHeightFat = 53; // 导航栏高度，有副标题
const iconSize = 50; // 图标尺寸
const iconButtonSize = 50;
const fixControlBarHeight = kScreenHeight > 600 ? 128 : 90;
const MAX_ANGLE = 101;
const MIN_ANGLE = 1;
const MAX_ELEVATION = 101;
const MIN_ELEVATION = 1;
const kIsCN = Util.isLanguageCN()

const TAG = "AudioVideoCallPage";
// const MHCameraCaptureView = requireNativeComponent('MHCameraCaptureView');
const PREVIEW_CAMERA_W = 100;
const PREVIEW_CAMERA_H = 150;
const PREVIEW_PADDING = Host.isPad ?  24 : 12;
const TOP_BAR_HEIGHT = StatusBarUtil._getInset("top");
const BOTTOM_BAR_HEIGHT = StatusBarUtil._getInset("bottom") <= 0 ? 25 : StatusBarUtil._getInset("bottom");
const GESTURE_X = PREVIEW_CAMERA_W + PREVIEW_PADDING;
const GESTURE_Y = PREVIEW_CAMERA_H + BOTTOM_BAR_HEIGHT;
let DEFAULT_SCALE_VALUE = kWindowWidth * 3 / 2 / (9 / 16 * kWindowWidth);
export default class AudioVideoCallPage extends React.Component {
  static navigationOptions = (navigation) => {
    // if (true) {//不要导航条
    //   return null;
    // }
    return {
      headerTransparent: true,
      header:
        null
    };
  };
  state = {
    prePositionNameTooLong: false,
    isCruising: false,
    inputPrePositionName: false,
    showPostionLetter: false,
    pstate: -1,
    error: -1,
    bps: 0,
    showPlayToolBar: true,
    useLenCorrent: true, // 畸变纠正
    darkMode: false,
    fullScreen: this.props.navigation.getParam("ori") === "LANDSCAPE",
    isCalling: false,
    isRecording: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    isFlip: false,
    rotation: 0,
    showDirectCtr: false,
    controlViewHeight: new Animated.Value(fixControlBarHeight),
    optionCoverAlpha: new Animated.Value(0.0),
    videoHeightScale: 1,
    angle: 51,
    elevation: 51,
    videoScale:  DEFAULT_SCALE_VALUE,
    angleViewShowScale: false,
    showCameraAngleView: false,
    showPanoView: false,
    showTargetPushView: false,
    isNewDevice: false,
    panoViewStatus: 1,
    editPonoView: false,
    panoDialogVisibility: false,
    savedVideoScale: 1.0,
    isOverExposed: false,

    dialogVisibility: false,
    screenshotVisiblity: false,
    screenshotPath: "",
    showDefaultBgView: true,
    whiteTitleBg: true,
    showErrorView: false,
    showLoadingView: true,
    showPoweroffView: false,
    showCloudVipBuyTip: false,
    errTextString: "",
    showPauseView: false,
    lastOfflineTime: "",
    isInternationServer: true, // 是否为国际服
    showRedDot: false,
    showNasRedDot: false,
    isWatermarkEnable: true,
    bannerId: 0,
    bannerShortKey: "0",
    clickedBannerShortKey: 0,
    recordTimeSeconds: 0,
    isWhiteVideoBackground: true,
    isNoneChinaLand: false,
    restoreOriFinished: true, // correct for 
    restoreOriFinished2: true,

    showLogDialog: false,
    logDialogContent: "",

    permissionRequestState: 0,
    showPermissionDialog: false,

    showGlobalLoading: false,// 全局的loading
    showTimeoutDialog: false,
    enableAIFrame: false,
    showGBFDialog: false,
    showOneKeyCallDialog: false,
    isAudioBtnDisabled: false,
    sdcardFormatDialog: false,
    sdcardFullDialog: false,
    sdcardSmallDialog: false,
    sdcardStatusInt: -1,
    bgImgUri: null,

    // 音视频通话相关状态
    audioOpen: true,
    videoOpen: false,
    frontCamera: true,
    offsetX: 0,
    offsetY: 0,
    scale: 0,
    cameraType: 0,
    minX: this.props.navigation.getParam("ori") === "LANDSCAPE" ? (Host.isPad && Host.isAndroid) ? TOP_BAR_HEIGHT + 110 : TOP_BAR_HEIGHT + 60 : kWindowWidth - GESTURE_X,
    minY: this.props.navigation.getParam("ori") === "LANDSCAPE" ? PREVIEW_PADDING : TOP_BAR_HEIGHT + 60,
    callTime: 0, // 通话时间
    tempCallVolumeValue: 30,
    callVolumeValue: 30,
    showChangeVolumeDialog: false,
    encodeWidth: 320,
    encodeHeight: 480,
    showCallDuration: false,
    isCallPrepare: true,
    isAudioCallInCommunication:false
  };

  constructor(props) {
    super(props);
    TrackConnectionHelper.trackLivePageEnter();

    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.fromOneKeyCall = props.navigation.getParam("fromOneKeyCall");
    this.startCallFlag = props.navigation.getParam("startCallFlag");
    // 0视频通话 1 语音通话 2麦克风关闭摄像头打开 3麦克风摄像头都关闭
    this.callType = props.navigation.getParam("callType");
    this.isCloudServer = CameraConfig.getIsCloudServer();

    this.userExpPlanPopup = true; //是否弹窗用户隐私弹窗
    this.privacyDialogPoped = false;
    this.needCheckUserExp = false;
    this.putedSD_STATUS_EJECTED = -1;
    this.isPageForeGround = true;// 默认当前page在前台, 页面在后，plugin可前可后；plugin在后，页面可前可后。
    this.isPluginForeGround = true;// 默认当前插件在前台
    this.isAppForeround = true;// 米家app是否在前台
    this.cameraViewIsMoving = false;
    this._createPanResponder();
    this.freeHomeSurExpireTime = ''; // 免费看家结束时间
    this.freeHomSurStatus = '';
    this.isFirstErrorEnter = true;
    this.isErrorToConnect = false;
    this.isNetworkToReconnect = false;
    this.isPhoneNetworkError = false;
    this.audioCMDError = false;
    this.videoCMDError = true;
    this.callErrorSigal = 0;
    this.firstSendCallCMD = true;
    this.callIsStop = false;
    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("test will focus");
        // this.state.darkMode ? StatusBar.setBarStyle('dark-content') : StatusBar.setBarStyle('light-content');//去往了其他页面，要主动刷新状态栏 为黑色
        StatusBar.setBarStyle('light-content');
        if(VersionUtil.Model_chuangmi_086ac1 == Device.model
          || VersionUtil.Model_xiaomi_096ac1 == Device.model){


          this.setState({isAudioCallInCommunication:true})
        }
      
        if (Platform.OS == "ios" && !this.isPluginForeGround) { // 如果是ios，插件跳到了原生页面，同时调用到了popToTop，package.willDisappear和didFocus都会被调用到，原来的逻辑就有问题。
          this.isPageForeGround = true;
          return;
        }
        console.log("landingpush 2", isStartUpPush());
        if (Platform.OS == "ios" && isStartUpPush() && !Host.isPad) { // ios pad 不退出插件，避免从push点过来，跳到原生播放页，原生播放页是小画面。
          Package.exit();
          return;
        }
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeround = true;
        console.log('testaaa', 'didFocusListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
        Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.AUTO });
        this._onResume();
      }
    );

    // didBlur在ios上调的时间晚，会在其他页面的onResume之后，换成willBlur
    this.willBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        this.state.darkMode ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');//去往了其他页面，要主动刷新状态栏 为黑色

        this.isPageForeGround = false;
        // LogUtil.logOnAll("CameraPlayer.getInstance().bindOneKeyCallReceived(null) by wilBlur");
        // CameraPlayer.getInstance().bindOneKeyCallReceived(null);
        console.log("will blur");
        if(VersionUtil.Model_chuangmi_086ac1 == Device.model
          || VersionUtil.Model_xiaomi_096ac1 == Device.model){
           this.setState({isAudioCallInCommunication:false})
        }
        this._onPause();
        if (this.cameraGLView != null && !this.isCheckingPermission) {
          this.cameraGLView.hidesSurfaceView();
        }
        Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.AUTO });
      }
    );

    // 操作系统栏时，ios会调用packageDidResume 和 packageWillPause，android不会，所以android不会有pause和resume流程
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume, this.isPageForeground:" + this.isPageForeGround + " this.isPluginForeground:" + this.isPluginForeGround + " isAppForeround:" + this.isAppForeround + " isOnRequestingPincode:" + this.isOnRequestingPincode + " isPowerOn:" + this.isPowerOn + " this.glview:" + (this.cameraGLView == null) + " isFirstEnter:" + this.isFirstEnter);
      if (!this.isPageForeGround) { // app进入前台，ios/android都会调用。对android，从native页面返回也会调用这个页面
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;// ios 插件不在前台 App从后台回到前台也会收到这个通知，
      }
      this.isAppForeround = true;// rnactivity调用了onresume
      // console.log('testaaa', 'didResumeListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
      if (Platform.OS == "android" && this.evenLockScreen > 0) {
        this.setState({ restoreOriFinished: false });
      }
      this.restoreOri(2);
      if (Host.isAndroid) {
        if (this.isOnRequestingPincode) {
          this.pinCodeSwitchChangedListener && this.pinCodeSwitchChangedListener.remove();
          this.isOnRequestingPincode = false;// 下一次走过来，就不用处理了。
          LogUtil.logOnAll("PincodeUtil", this.isOnRequestingPincode + " " + this.isPinCodeSet);
          // 如果是从密码输入页面回来的，此时要判断pincodeSwitchChangeEvent里返回的值。
          if (this.isPinCodeSet) {
            // this.checkPrivacyDialog();// 开始处理隐私弹框 调用流程改了，这里不用再去处理密码弹框了
          } else {
            // 没有开启密码，退出插件。
            console.log("=====+=================exit")
            Package.exit();
            return;//这里是android
          }

        }
      }
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (this.iosPermissionRequest && Platform.OS === "ios") {
        console.log("packageWillPause","ios permission request");
        return;
      }
      Toast._hideLastToast();
      if (!this.isPageForeGround) { // app进入后台，ios/android都会调用。对android，进入native页面也会调用这个页面
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;// ios平台回到后台，默认都会发这个消息，如果插件不在前台，就不处理。
      }
      this.isAppForeround = false;// rnactivity调用了onpause
      // console.log('testaaa', 'willPauseListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
      console.log("will pause");
      this._onPause();

      if (Platform.OS == "android" && this.state.fullScreen) {
        this.evenLockScreen = 2;// lock screen or go home
      }


    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {// RN插件

        // if (!this.isPluginForeGround) { // 从native页回来后，ios只进入这一个状态，但是android还要走packageDidResume，所以这里要为ios调用onResume
        //   // CHUANGMI-9510，从Native页面返回需要在其他函数中onResume
        //   this.isPluginForeGround = true;
        //   return;
        // }
        if (!this.isPageForeGround) {
          return;
        }
        if (isStartUpPush()) {
          Package.exit();
          return;
        }
        // this.isPageForeGround = true;// only for ios jump to native view, statusbar control
        this.isPluginForeGround = true;// rnactivity调用了onresume
        // console.log('testaaa', 'willAppearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
        if (!Host.isAndroid) {
          if (this.isOnRequestingPincode) {
            this.pinCodeSwitchChangedListener && this.pinCodeSwitchChangedListener.remove();
            this.isOnRequestingPincode = false;// 下一次走过来，就不用处理了。
            LogUtil.logOnAll("PincodeUtil", this.isOnRequestingPincode + " " + this.isPinCodeSet);
            // 如果是从密码输入页面回来的，此时要判断pincodeSwitchChangeEvent里返回的值。
            if (this.isPinCodeSet) {
              // this.checkPrivacyDialog();// 开始处理隐私弹框 调用流程改了，这里不用再去处理密码弹框了
            } else {
              // 没有开启密码，退出插件。
              Package.exit();
              return;//这里是android
            }

          }
        }
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 进入native页面，ios只调用这个页面。
          return;
        }

        // this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          console.log("ios disappear");
          this._onPause();

        }, 0);
      });

      this.lastRecordTime = "00:00";
    } else {
      // TODO 10048中打开
      if (PackageEvent.packageWillStopAndroid) {
        this.wllStopListener = PackageEvent.packageWillStopAndroid.addListener(() => {
          if (this.cameraGLView != null && !this.destroyed) {
          }
        });
      }
    }
    this.prePositions = [];
    this.prePositionItems = [];
    this.PreSetCTRL = "ctrl";
    this.PreSetADD = "set";
    this.PreSetDELETE = "del";
    this.preSetPositionImg = "preSetPosition_";
    let timeStamp = Date.now();
    this.preSetPositionImgTime = [timeStamp, timeStamp, timeStamp];
    this.ctrlCurrentLocation = [0, 0, 0];
    this.preSetPositionExist = false;
    this.addPreSetIndex = 1;
    this.addPreSetLocation = 1;
    this.angleData = { "ret": 0, "angle": 0, "elevation": 0 };
    this.currentNetworkState = -1;
    this.isChangingNetwork = false;
    this.skipDataWarning = false;// 默认不跳过流量控制
    this.checkIsInternationalServer();
    this.sdcardCode = -1;
    this.isFirstEnter = true;
    this.connRetry = 2;// only for first connection
    this.startVideoRetry = false;
    this.cloudVipEndTime = 0;
    this.cloudVipWillEndDays = 0;
    this.enablePtzRotation = true; // 云台手势转动
    this.videoRecordPath = null;

    this.voiceBtn = null;
    this.snapBtn = null;
    this.recordBtn = null;
    this.controlBtn = null;

    this.exitListener = PackageEvent.packageWillExit.addListener(() => {
      this.exitListener.remove();
      Service.miotcamera.disconnectToDevice();// 只调用一次。
      removeDarkListener();//退出插件的时候，移除监听器。
    });
    this.cancelAuthListener = PackageEvent.packageAuthorizationCancel.addListener(() => {
      StorageKeys.PANORAMA_IMAGE_PATH = "";
      this.cancelAuthListener.remove();
      Service.miotcamera.disconnectToDevice();
      StorageKeys.IS_PRIVACY_NEEDED = true;// 撤销授权后 应该重置状态为需要弹框
      StorageKeys.OPERATION_CLICKED_KEY = 0;
      Package.exit();
    });

    this.isAudioMuteTmp = CameraConfig.getUnitMute();

    this.cameraGLView = null;
    this.sleepDialog = null;

    this.angleView = null;
    this.isConnecting = false;

    this.isClickSleep = false;
    this.isClickCall = false;
    // this.mOri = "PORTRAIT";
    this.mOri = props.navigation.getParam("ori") ? props.navigation.getParam("ori") : "PORTRAIT";
    this.isCheckingPermission = false;
    this.iconAnimatedValue = new Animated.Value(0);
    this.panoramaType = 1;// 整型，0代表360°、1代表270°、2代表180°

    StorageKeys.LAST_PANO_ANGLE.then((res) => {
      if (typeof (res) == "string" || res == null) {
        res = 1;
        StorageKeys.LAST_PANO_ANGLE = 1;
      }
      this.panoramaType = res;
    });
    this.isAllViewRpc = false;// 是否在绘制中


    this.showPanoAfterReceivedRotateAngle = false;
    this.minSelectPositionLeft = 0;
    this.maxSelectPositionRight = 0;
    this.panoramaImgStoreUrl = null;
    this.isPtz = CameraConfig.isPTZCamera(Device.model);
    this.isHorizontalPTZ = CameraConfig.isHorizontalPTZ(Device.model);
    this.isNewChuangmi = CameraConfig.isNewChuangmi(Device.model);
    this.isSupportPhysicalCover = CameraConfig.isSupportPhysicalCover(Device.model);

    this.isSupport2K = CameraConfig.isSupport2K(Device.model);
    this.isSupport25K = CameraConfig.isSupport25K(Device.model);
    this.isSupport3K = CameraConfig.isSupport3K(Device.model);

    this.hideBgBpsCount = 0;

    this.detectionSwitch = false; // 看家设置是否打开
    this.evenLockScreen = 0;
    this.startScaleTime = 0;
    this.startScaleReportTime = 0;

    this.directionEndHintTimeout = null;
    this.loadingRetryTimer = 0;

    this.isHandlingSdcard = false;

    this.isSendingRdtCmd = false;
    this.enterLiveVideoTime = 0;
    this.enterCallTime = 0;
    this.delayPauseTimer = 0;
    this.destroyed = false;
    this.mFirmwareSupportCloud = VersionUtil.isFirmwareSupportCloud(Device.model); // 是指该设备的是否有能力支持云存：固件是否能支持云存或者是支持云存的地区

    this.videoPortraitScale = 1;
    this.isOnRequestingPincode = false;
    this.isPinCodeset = false;// 默认密码没有设置，需要配合isOnRequstingPinCode使用。

    AlbumHelper.fetchDeviceAlbumName(); // 提前刷新nativeAlbumName
    this.isIphone12 = this._isIphone12();
    this.lastTimeRecordBtnClicked = 0;

    this.videoQualityFetched = false;// videoquality is fetched
    this.lastTimeRecordBtnClicked = 0;

    this.privacySDKUtil = new PrivacySDKUtil();
    this.isFirmwareUpdating = false;

    this.isPrivacyAuthed = false;

    this.isFirstFrameReceived = false;

    this.selectedIndexArray = [0];

    DldMgr.addLdrs(SdcardEventLoader.getInstance());// 进来的时候调用一次 绑定sdcard下载器

    this.itemWidthP = kWindowWidth / 3;
    this.itemWidth = kWindowWidth * 0.28;
    this.itemHeight = this.itemWidth * 0.65;
    this.jianju = (this.itemWidthP - this.itemWidth) / 4.1;
    this.startvideoTime = 0;
    this.startAudioTime = 0;
    this.isStartCallError = false;
  }

  _isIphone12() {
    let vv = Host.systemInfo;
    console.log("currentSystemInfo:" + JSON.stringify(vv));
    let isiPhone12 = (vv.mobileModel.indexOf('iPhone13') == 0);
    return isiPhone12;
  }

  _getKanjiaSetting() {
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.getSpecAlarmConfig(2).then((result) => {
        if (result instanceof Array) {
          if (result.length > 0 && result[0].code == 0) {
            this.detectionSwitch = result[0].value;
          }
        }
      }).catch((err) => {
        console.log(`getSpecAlarmConfig err=${ JSON.stringify(err) }`);
      });
    } else {
      // API.get('/miot/camera/app/v1/get/alarmSwitch', 'business.smartcamera').then((res) => {
      AlarmUtil.getAlarmConfig().then((res) => {
        if (res.code == 0) {
        } else {
          console.log("get alarmconfig:", JSON.stringify(-1));
          return;
        }
        this.detectionSwitch = res.data.motionDetectionSwitch.detectionSwitch;
      }).catch((err) => {
        console.log("get alarmconfig err:", JSON.stringify(err));
      });
    }
  }

  _loadWaterMarkInfoFromCache() {
    // if (!VersionUtil.judgeIsV1(Device.model)) {
    //   return;
    // }
    StorageKeys.IS_WATERMARK_OPEN.then((res) => {
      if (res === "" || res == null) {
        res = true;
        StorageKeys.IS_WATERMARK_OPEN = true;
      }
      this.setState({
        isWatermarkEnable: res
      });
    }).catch(() => {
      this.setState({
        isWatermarkEnable: false
      });
    });
  }

  componentDidMount() {
    console.log("============--------------+++++",StatusBarUtil._getInset("top"),Dimensions.get("window").height, Dimensions.get("window").width,Dimensions.get("screen").height, Dimensions.get("screen").width);

    this.checkIsInternationalServer();
    CameraPlayer.getInstance().bindOneKeyCallReceived(this._receivedOneKeyCall.bind(this));
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindCallStateReceived(this._receivedCallStateChange.bind(this));
    CameraPlayer.getInstance().bindWaterTimeCallback(this._waterTimeChangeHandler);
    CameraPlayer.getInstance().bindPauseAllCallback(() => {
      this._stopAll(false, false);
    });
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    if (this.fromOneKeyCall) {
      this.onPrivacyAuthed();
    } else {
      this.checkPrivacyDialog();
    }
    // this._initCall();
    Dimensions.addEventListener('change', this.dimensionListener);
    Service.smarthome.reportLog(Device.model, "com.xiaomi.cardcamera begin to init; rn package version: 28 ");

    Host.ui.keepScreenNotLock(true);

    this.restoreOri(3);




    this.isVip = false;
    this.mVipStatus = 2;

    this.isPowerOn = null;
    this.isInWindow = false;

    this.add_often_look_position = DeviceEventEmitter.addListener('ADD_OFTEN_LOOK_POSITION', (msg) => {
      this._showDirectionViewAnimated(true);
      if (!this.state.showPanoView) this._toggleSwitch();
    });
    this.goBackSubScription = DeviceEventEmitter.addListener('goBack', (message) => {
      console.log("test will focus");
      this.sdCardabnormal();// sd卡异常监测
      if (Platform.OS == "ios" && !this.isPluginForeGround) { // 如果是ios，插件跳到了原生页面，同时调用到了popToTop，package.willDisappear和didFocus都会被调用到，原来的逻辑就有问题。
        this.isPageForeGround = true;
        return;
      }
      console.log("landingpush 2", isStartUpPush());
      if (Platform.OS == "ios" && isStartUpPush() && !Host.isPad) { // ios pad 不退出插件，避免从push点过来，跳到原生播放页，原生播放页是小画面。
        Package.exit();
        return;
      }
      this.isPageForeGround = true;
      this.isPluginForeGround = true;
      this.isAppForeround = true;
      console.log('testaaa', 'didFocusListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
      Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.ALWAYS_PLUGIN_DEAL });
      this._onResume();
    });
    this.bpsListener = DeviceEventEmitter.addListener(kBpsDataReceiveCallbackName, ({ data }) => {
      if (!this.props.navigation.isFocused()) {
        return;
      }
      if (!this.isPageForeGround) {
        return;// 直播页不在前面就不展示
      }
      if (!this.isAppForeround) {
        return;
      }
      if (!this.isPluginForeGround) {
        return;
      }

      if (data > 0 && (this.state.showDefaultBgView || this.state.showLoadingView)) {
        this.hideBgBpsCount += 1;
        if (this.hideBgBpsCount >= 8) {
          this.setState({ showDefaultBgView: false, whiteTitleBg: false, showLoadingView: false });
        }
      }
      this.setState({ bps: data });
    });
    this.recordListener = DeviceEventEmitter.addListener(kRecordTimeCallbackName, (data) => {
      if (!this.props.navigation.isFocused()) {
        return;
      }
      if (!this.isPageForeGround || data == null) {
        return;
      }
      let time = Number.parseInt(data.recordTime);
      this.setState({ recordTimeSeconds: time });
      console.log(data);// 录制时长。
      LogUtil.logOnAll(JSON.stringify(data),"kRecordTimeCallbackName");// 录制时长。
    });

    this.frameQualityListener = DeviceEventEmitter.addListener(KFirstFrameOverExposedResultCallBackName, (data) => {
      LogUtil.logOnAll(TAG, "receive over exposed event", data);
      if (!this.props.navigation.isFocused()) {
        LogUtil.logOnAll("frameQualityListener isFocused = false");
        return;
      }
      // result=1 过度曝光 2 图片非常黑 无有效信息  3 图片像灰度图 4 正常
      let result = Number.parseInt(data.result);
      this.setState({ isOverExposed: result == "1" ? true : false });
      if (CameraConfig.isNewChuangmi(Device.model)) {
        LogUtil.logOnAll(`AlarmUtil.status_ejected=${ AlarmUtil.status_ejected }`);
        if (AlarmUtil.status_ejected != -1) {
          this._getTargetPush(AlarmUtil.status_ejected == 1);
        } else {
          AlarmUtil.getSD_STATUS_EJECTED().then((result) => {
            LogUtil.logOnAll("getSD_STATUS_EJECTED by frameQualityListener=", JSON.stringify(result));
            if (result.length > 0 && result[0].code == 0) {
              AlarmUtil.status_ejected = result[0].value ? 0 : 1;
              this._getTargetPush(!result[0].value);
            } else {
              this._getTargetPush(true);
            }
          }).catch((err) => {
            LogUtil.logOnAll("getSD_STATUS_EJECTED by frameQualityListener err =", JSON.stringify(err));
            this._getTargetPush(true);
          });
        }
      }
    });

    Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);

    // this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    this._fetchVipStatus.bind(this);

    StorageKeys.LIVE_VIDEO_RESOLUTION.then((result) => {
    if (typeof (result) == "string" || result == null || result == undefined) {
        if (Device.model == CameraConfig.Model_chuangmi_069a01) {
          StorageKeys.LIVE_VIDEO_RESOLUTION = 3;// 设置默认sp
          result = 3;
        } else {
          StorageKeys.LIVE_VIDEO_RESOLUTION = 0;// 设置默认sp
          result = 0;
        }
      }
      this.videoQualityFetched = true;
      if (CameraPlayer.getInstance().isConnected()) { // sp缓存取回的时候，如果已经连接，就发送video-quality
        this._changeResolution(result);
      }
      this._setIndexOfResolution(result);
      this.setState({ resolution: result });
    })
      .catch((err) => {
        console.log(err);
        this.videoQualityFetched = true;
        let result = 0;
        if (Device.model == CameraConfig.Model_chuangmi_069a01) {
          result = 3;
        }
        this._setIndexOfResolution(result);
        this.setState({ resolution: result });
      });
    setTimeout(() => {
      this.loadLocalSetttings();
    }, 500);// 延迟执行。

    if (this.isPtz) {
      this.bindRdtListener();
    }

    this.language = Host.locale.language || "en";
    let isNoneChinaLand = this.language != "zh" && this.language != "zh_tw" && this.language != "zh_hk";
    this.setState({ isNoneChinaLand: isNoneChinaLand, isZH: this.language == 'zh' });
    // AlbumHelper.checkStoragePermission();

    // this._loadMonitoringDetailTimer = setTimeout(() => {
    //   this._getKanjiaSetting();
    // }, 1000);

    this.bannerItem = null;
    this.bannerImg = null;
    this.targetpushItem = null;


    this.isReadonlyShared = Device.isReadonlyShared;

    let version = Device.lastVersion;
    let verNumber = VersionUtil.calculateVersionValue(version);
    this.panoParam = CameraConfig.getPanoramaParam(Device.model, verNumber);

    // if (VersionUtil.isFirmwareSupportColloctPrivacyLog(Device.model)) {
    //   this.startCheckLogTimer = setTimeout(() => {
    //     this.startCheckLog();
    //   }, 2000);// 延迟rpc，避免跟其他rpc一起打架
    // }

    TrackUtil.reportClickEvent("Camera_Connect_Num");// Camera_Connect_Num 直播页访问人数/次数

    //查看共享设备
    console.log(Device.permitLevel, Device.model, 'permitLevel');
    //16是自己的
    //4是被分享的
    //36是仅可查看
    //用本地存储 记得判断是021 029
    if (CameraConfig.isSupportVisitInfo(Device.model) && !Device.isOwner) {
      this._showVisitInfo();
    }


    this._fetchVipStatus();

    let colorScheme = DarkMode.getColorScheme();

    if (colorScheme == 'dark') {
      this.setState({ darkMode: true });
    } else {
      this.setState({ darkMode: false });
    }

    StorageKeys.NEED_SHOW_PRE_POSITION_DIALOG.then((res) => {
      console.log("NEED_SHOW_PRE_POSITION_DIALOG res:", res);
      if (!res) {
        this.setState({ showPostionLetter: true });
        StorageKeys.NEED_SHOW_PRE_POSITION_DIALOG = true;
      }
    }).catch((err) => {
      console.log("NEED_SHOW_PRE_POSITION_DIALOG err:", err);
    });
    if (Platform.OS === 'ios') {
      StorageKeys.VIDEO_SCALE.then((result) => {
        this.setVideoScale();
      }).catch((err) => {
        this.setVideoScale();
      });
    }

    LogUtil.logOnAll("----=-=-=-=-=-=-=-=-=", JSON.stringify(Package.entryInfo));
    // Package.entryInfo={"open_plugin_api_std_try_check":"1650024897435","open_plugin_cached":"true","open_plugin_api_get_available_cost":"1","process_reuse_enter_type":"0","open_plugin_api_plugin_ready":"1650024897437","core_ready_to_plugin_cache_ready_cost":"0","open_plugin_click_start_time":"1650024897434","open_plugin_api_send_message_internal":"1650024897437","open_plugin_plugin_process_open_activity":"1650024897453","openTime":"1650024897457","extra_click_device_time":"1650024897457","ensure_service_success_to_ensure_service_main_thread_cost":"0","open_plugin_plugin_process_load_rn":"1650024897453","did":"1070202740","package_msgType":"2","time":"1650024897","type":"ScenePush","event":"12.1","open_plugin_downloaded":"true","extra":"[]","model":"chuangmi.camera.051a01","work_thread_to_ensure_service_start_cost":"2","plugin_cache_ready_to_work_thread_cost":"0","rev_message_to_core_ready_cost":"1","open_plugin_plugin_process_rev_message":"1650024897449","open_plugin_api_sdk_check_background":"false","isNotified":"false","ensure_service_start_to_ensure_service_success_cost":"0","open_plugin_plugin_process_init_rn_device_start":"1650024897457","open_plugin_plugin_process_init_rn_device":"true","open_plugin_api_send_message_type":"11","open_plugin_main_process_send_message_inner":"1650024897446","open_plugin_main_process_send_message_proxy":"1650024897446","pageParams":"{}"}
    // if (VersionUtil.Model_chuangmi_051a01 == Device.model) {
    //   if (Package.entryInfo.type == "ScenePush" && Package.entryInfo.event == CameraPlayer.oneKeyCallEvent_051a01) {
    //     LogUtil.logOnAll("----=-=-=-=-=-=-=-=-= received onekeycall ");
    //     AlarmUtil.getOneKeyCallStatus().then((res) => {
    //       LogUtil.logOnAll("received onekeycall res=", JSON.stringify(res));
    //       if (res[0].value == 1) {
    //         LogUtil.logOnAll("received onekeycall 111");
    //         this._showOneKeyCallDialog();
    //       } else if (res[0].value == 2) {
    //         Toast._showToast(LocalizedStrings["one_key_call_talking"]);
    //       } else {
    //         Toast._showToast(LocalizedStrings["one_key_call_talkend"]);
    //       }
    //     }).catch((err) => {
    //       LogUtil.logOnAll("received onekeycall err=", JSON.stringify(err));
    //     });
    //   }
    // }
    // CameraPlayer.getInstance().receivedOneKeyCall();
    AlarmUtil.loadAutomaticScenes(Device.deviceID).then(() => {
      console.log("LiveVideoPageV2 loadAutomaticScenes res==", AlarmUtil.sceneSize);
    }).catch((err) => {
      console.log("LiveVideoPageV2 loadAutomaticScenes err==", JSON.stringify(err));
    });
    AlarmUtil.status_ejected = -1;

    this.getCallVolume();
  }

  getCallVolume() {
    let params = [{ sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME }];
    AlarmUtilV2.getSpecPValue(params)
      .then((res) => {
        if (res[0].code === 0) {
          this.setState({ callVolumeValue: res[0].value, tempCallVolumeValue: res[0].value });
        }
      }).catch((err) => {
        console.log("get call volume error");
      });
  }

  setVideoScale() {
    setTimeout(() => {
      let scale = this.props.navigation.getParam("ori") === "LANDSCAPE" ? 1 : DEFAULT_SCALE_VALUE;
      this.setState({ savedVideoScale: scale, videoScale: scale });
    }, 50);
  }

  _startCallTimeInterval() {
    this.callTimeInterval && clearInterval(this.callTimeInterval);
    this.callStartMls = new Date().getTime();
    console.log("===================_startCallTimeInterval0:",this.callStartMls);
    this.callTimeInterval = setInterval(() => {
      let testDateMls = new Date().getTime();
      console.log("===================_startCallTimeInterval1:",testDateMls,testDateMls-this.callStartMls,(testDateMls-this.callStartMls)/1000,Math.round((testDateMls-this.callStartMls) / 1000));
      const newCallTime = Math.round((new Date().getTime()-this.callStartMls) / 1000);
      this.setState(prevState => ({ callTime: newCallTime }));

      // this.setState({ callTime: newCallTime });
    }, 1000); // 每隔1秒更新一次通话时间
  }

  _showOneKeyCallDialog() {
    // this.setState({ showOneKeyCallDialog: true });
    // this.oneKeyCallTimer && clearInterval(this.oneKeyCallTimer);
    // this.oneKeyCallTimer = setInterval(this._getOneKeyCallStatus.bind(this), 5000);
  }

  _doGoBack() {
    console.log("====back=======",this.isPageForeGround);
    if (!this.isPageForeGround) {
      return;
    }
    let rootStack = getStack();
    const routes = rootStack._navigation.state.routes;
    console.log("====back=======",routes.length);
    const { state } = this.props.navigation; 
    if (routes.length > 1) {
      state.params && state.params.callback && state.params.callback();
      this.props.navigation.goBack();
    
    } else {
      Package.exit();
    }
  }

  _getOneKeyCallStatus() {
    // AlarmUtil.getOneKeyCallStatus().then((res) => {
    //   LogUtil.logOnAll("getOneKeyCallStatus res=", JSON.stringify(res));
    //   if (res[0].value != 1) {
    //     LogUtil.logOnAll("getOneKeyCallStatus clearInterval");
    //     this.oneKeyCallTimer && clearInterval(this.oneKeyCallTimer);
    //     this.setState({ showOneKeyCallDialog: false });
    //     if (res[0].value == 2) {
    //       Toast._showToast(LocalizedStrings["one_key_call_talking"]);
    //     } else {
    //       Toast._showToast(LocalizedStrings["one_key_call_talkend"]);
    //     }
    //   }
    // }).catch((err) => {
    //   LogUtil.logOnAll("getOneKeyCallStatus err=", JSON.stringify(err));
    // });
  }

  _getOneKeyCallStatusWithoutToast() {
    AlarmUtil.getOneKeyCallStatus().then((res) => {
      LogUtil.logOnAll("_getOneKeyCallStatusWithoutToast res=", JSON.stringify(res));
      if (res[0].value != 1) {
        LogUtil.logOnAll("_getOneKeyCallStatusWithoutToast showOneKeyCallDialog: false");
        this.setState({ showOneKeyCallDialog: false });
      }
    }).catch((err) => {
      LogUtil.logOnAll("_getOneKeyCallStatusWithoutToast err=", JSON.stringify(err));
    });
  }

  _receivedOneKeyCall(value) {
    console.log("======++=+===+=+=+==+====call state change",value);
    if (value == 3) {
      this.isDeviceHangup = true;
      Toast.success("device_hangup");
      // 表示设备端挂断了通话
      if (this.state.fullScreen) {
        this.restoreOri(5);
      }
      this._stopAll();
      this._doGoBack();
    }
  }

  _receivedCallStateChange(value) {
    console.log("callPage=====================通话状态==============",value);
    // 此逻辑先注释掉
    // if (value == 0) {
    //   console.log("callPage=====================通话状态==============0")
    //   Toast._showToast(LocalizedStrings["one_key_call_talkend"]);
    //   this._stopAll();
    //   this._doGoBack();
    // }
  }

  _receivedOneKeyCall_bak() {
    this.fromOneKeyCall = this.props.navigation.getParam("fromOneKeyCall");
    this.startCallFlag = this.props.navigation.getParam("startCallFlag");
    LogUtil.logOnAll("_receivedOneKeyCall packageReceivedInformation6666", "startCallFlag=", this.startCallFlag);
    if (!CameraPlayer.getInstance().isConnected()) {
      LogUtil.logOnAll("_receivedOneKeyCall talk_for_push_connecting");
      // Toast.fail('talk_for_push_connecting');
      return;
    }
    // LogUtil.logOnAll(" one key call startCall ");
    // this._startCall();
    AlbumHelper.getCachedImage()
      .then((result) => {
        let bgImgUri = `file://${ Host.file.storageBasePath }/${ result }`;
        this.setState({ bgImgUri: bgImgUri });
      });
  }

  _showVisitInfo() {
    console.log('共享设备才会弹窗');
    StorageKeys.IS_VISIT_PUSH_SHOWN
      .then((res) => {
        if (typeof (res) === "string" || res == null) {
          this.setState({
            isVisitShow: true
          });
          console.log('没有显示过弹窗', res);
        } else {
          console.log('已经显示过弹窗', res);
          this.setState({
            isVisitShow: false
          });
        }

      })
      .catch((err) => {
        // isVisitShow = false
        console.log('显示弹窗出错了', err);
      });
  }

  _bindPinCodeSwitchChangedEvent() {
    this.pinCodeSwitchChangedListener = DeviceEvent.pinCodeSwitchChanged.addListener((device, result) => {
      LogUtil.logOnAll("PincodeUtil", "pinCodeSwitchChanged" + JSON.stringify(result));
      this.isPinCodeSet = result.isSetPinCode;// 密码发生了改变，通知到插件，插件里暂存；
      if (this.isPinCodeSet) {
        PinCodeUtil.setPincodeSet();
      }
    });
  }

  _clearTimer(timer) {
    if (timer) {
      clearTimeout(timer);
    }
  }

  componentWillUnmount() {
    this.oneKeyCallTimer && clearInterval(this.oneKeyCallTimer);
    this.callTimeInterval && clearInterval(this.callTimeInterval);
    this.cruiseTimer && clearTimeout(this.cruiseTimer);
    try {
      this._stopAll(false, false);
    } catch (exception) {

    }
    this.privacySDKUtil.destroyPrivacyListener();
    clearTimeout(this.callTimeout);
    removeDarkListener();
    this.destroyed = true;
    OfflineHelper.resetLastOfflineTime();
    this._clearTimer(this.angleViewTimeout);
    clearInterval(this.logInterval);
    this._clearTimer(this.mFirmwareUpdatingTimer);
    this._clearTimer(this._loadMonitoringDetailTimer);
    this._clearTimer(this.startCheckLogTimer);
    this._clearTimer(this.showNetworkDisconnectTimeout);
    this._clearTimer(this.reconnectTimeout);
    this._clearTimer(this.showPlayToolBarTimer);
    this._clearTimer(this.longPressTimer);
    this._clearTimer(this.angleViewTimeout);
    this._clearTimer(this.mRpcCallTimer);
    this._clearTimer(this.mGetRotationTimer);
    this._clearTimer(this.longPressTimer);
    this._clearTimer(this.mSetAudioBtnStateTimer);
    this._clearTimer(this.snapshotTimeout);
    this._clearTimer(this.mRecordOkTimer);
    this._clearTimer(this.connectTimeout);
    this._clearTimer(this.startCallAgainTimeout);
    this._clearTimer(this.delayToGetCallStatus);
    this._clearTimer(this.getPadInfoDelay);
    Dimensions.removeEventListener('change', this.dimensionListener);

    if (this.props.navigation.getParam("ori") !== "LANDSCAPE") {
      this.toPortrait(3);
    }


    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.wllStopListener && this.wllStopListener.remove();
    Host.ui.keepScreenNotLock(true);
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    this.willBlurListener && this.willBlurListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    this.bpsListener && this.bpsListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.recordListener.remove();
    LogUtil.logOnAll("CameraPlayer.getInstance().bindOneKeyCallReceived(null) by componentWillUnmount");
    CameraPlayer.getInstance().bindOneKeyCallReceived(null);
    CameraPlayer.getInstance().bindCallStateReceived(null);
    CameraPlayer.getInstance().bindWaterTimeCallback(null);
    this.frameQualityListener && this.frameQualityListener.remove();
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    Orientation.removeOrientationListener(this._orientationListener);
    this.delayWorkTimeout && clearTimeout(this.delayWorkTimeout);
    this.waterDelay && clearTimeout(this.waterDelay);
    this.logDelay && clearTimeout(this.logDelay);
    this.delayGetPreSetPosition && clearTimeout(this.delayGetPreSetPosition);
    this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
    if (this.rdtListener) {
      this.rdtListener.remove();
      this.rdtListener = null;
    }
    TrackConnectionHelper.report();
    this.add_often_look_position && this.add_often_look_position.remove();
    // destroy的步骤尽量不要混乱，dldMgr持有sdcardEventLoader，SdcardEventLoader持有CameraPlayer，尽量一个一个按顺序的处理
    this.setState = () => false;
  }


  dimensionListener = (args) => {
    if (!this?.props?.navigation?.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          Service.smarthome.reportLog(Device.model, `dimensionListener fullscreen? ${ this.state.fullScreen }`);
          console.log('纠正========');
        }
      }
    }
  };

  _createPanResponder() {

    this._touchStartPageY = 0;
    this._touchStartCtrHeight = 0;
    this._controlViewHeight = this.state.showDirectCtr ? fixControlBarHeight + this._getDirectionContainerHeight() : fixControlBarHeight;
    this.state.controlViewHeight.addListener(({ value }) => this._controlViewHeight = value);

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => false,
      onPanResponderTerminationRequest: () => false, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        // this.setState({ showDirectCtr: true });
      },

      onPanResponderMove: (evt) => {
        let y = evt.nativeEvent.locationY;
        let pageY = evt.nativeEvent.pageY;
        // if (this._touchStartPageY == 0 && y < (fixControlBarHeight - 20)) {
        //   return;
        // }

        if (this._touchStartPageY == 0) {
          this._touchStartPageY = pageY;
          this._touchStartCtrHeight = this._controlViewHeight;
        }

        let delta = pageY - this._touchStartPageY;
        let newHeight = this._touchStartCtrHeight + delta;

        if (newHeight > fixControlBarHeight + this._getDirectionContainerHeight()) {
          newHeight = fixControlBarHeight + this._getDirectionContainerHeight();
        }

        if (newHeight < fixControlBarHeight) {
          newHeight = fixControlBarHeight;
        }

        let newAlpha = (newHeight - fixControlBarHeight) / this._getDirectionContainerHeight();
        newAlpha = newAlpha * 0.5;

        this.state.controlViewHeight.setValue(newHeight);
        this.state.optionCoverAlpha.setValue(newAlpha);
      },

      onPanResponderRelease: () => {
        this._touchStartPageY = 0;
        let shouldShow = this._controlViewHeight >= fixControlBarHeight + this._getDirectionContainerHeight() / 2 ? true : false;
        this._showDirectionViewAnimated(shouldShow);
      },

      onPanResponderTerminate: () => {
      }
    });

    this.myPanResponder = PanResponder.create({
      // 要求成为响应者：
      onStartShouldSetPanResponder: (evt, gestureState) => {
        // let isMulti = evt.nativeEvent.changedTouches.length > 1;
        // console.log(`onStartShouldSetPanResponder ${ isMulti }`);
        // if (isMulti) { // 多指才拦截
        return true;
        // } else {
        //     return false;
        // }
      },
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
        return true;
        // } else {
        //     return false;
        // }
      },
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // let isMulti = evt.nativeEvent.changedTouches.length > 1;
        // // console.log("onMoveShouldSetPanResponder:" + isMulti);
        // if (isMulti) { // 多指才拦截
        return true;
        // } else {
        //     return false;
        // }
      },
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
        // console.log('000000');
        // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
        return true;
        // } else {
        //   return false;
        // }
      },
      onPanResponderTerminationRequest: (evt, gestureState) => false,

      // 响应对应事件后的处理:
      onPanResponderGrant: (evt, gestureState) => {
        console.log('平移----');
        console.log('平移--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
        this._top = this.state.minY;
        this._left = this.state.minX;
        this.forceUpdate();
      },
      onPanResponderMove: (evt, gestureState) => {
        this.cameraViewIsMoving = true;
        // this.padScreenHeightHorizontal
        // this.padScreenWidthHorizontal
        let Height = this.state.fullScreen ? Host.isPad ? Math.min(this.padScreenHeightHorizontal,this.padScreenWidthHorizontal) : kWindowWidth : kScreenHeight;
        let width = this.state.fullScreen ? Host.isPad ? Math.max(this.padScreenHeightHorizontal,this.padScreenWidthHorizontal) : kScreenHeight : kWindowWidth;
        // let Height = this.state.fullScreen ? Host.isPad ? this.padScreenHeightHorizontal : kWindowWidth : kWindowHeight;
        // let width = this.state.fullScreen ? Host.isPad ? this.padScreenWidthHorizontal : kWindowHeight : kWindowWidth;
        let minX = this._left + gestureState.dx;

        let xPadding = this.state.fullScreen ?  Host.isPad ? PREVIEW_PADDING : TOP_BAR_HEIGHT : PREVIEW_PADDING;
        let pvWidth = this.state.fullScreen ? PREVIEW_CAMERA_H : PREVIEW_CAMERA_W;
        let xPaddingMax = this.state.fullScreen ? Host.isPad ? width - pvWidth - PREVIEW_PADDING : width - pvWidth - TOP_BAR_HEIGHT : width - GESTURE_X;
        // console.log('平移--isMuit--minX11',minX,xPadding,xPaddingMax);
        minX = minX <= xPadding ? xPadding : minX;
        minX = minX > xPaddingMax ? xPaddingMax : minX;

        let minY = this._top + gestureState.dy;
        console.log('平移--isMuit--minY22',minY);
        let yPadding = this.state.fullScreen ? PREVIEW_PADDING : TOP_BAR_HEIGHT;
        let pvHeight = this.state.fullScreen ? PREVIEW_CAMERA_W : PREVIEW_CAMERA_H;
        let yPaddingMax = this.state.fullScreen ? Height - pvHeight - PREVIEW_PADDING : Height - GESTURE_Y;
        minY = minY <= yPadding ? yPadding : minY;
        minY = minY > yPaddingMax ? yPaddingMax : minY;
        this.setState({ minX: minX, minY: minY });

        console.log('平移-isMuit--false-show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, minX, this.state.maxY);

      },
      onPanResponderRelease: (evt, gestureState) => {
        // this.setState( {eventName:'抬手'} );
        this.zoomLastDistance = 0;
        this.cameraViewIsMoving = false;
        // 画中画只能两侧显示
        let padShortLine = Math.min(this.padScreenHeightHorizontal,this.padScreenWidthHorizontal);
        let kw = this.state.fullScreen && Host.isPad ? padShortLine : kWindowWidth;
        console.log("onPanResponderRelease1",this.padScreenHeightHorizontal,this.padScreenWidthHorizontal,kWindowWidth,this.state.fullScreen,Host.isPad);
        if (this.state.fullScreen) {
          let sideX = kw / 2 - GESTURE_X / 2;
          if (this.state.minY <= sideX) {
            this.setState({ minY: PREVIEW_PADDING });
            console.log("onPanResponderRelease2",PREVIEW_PADDING);
          } else {
            let yHeight = this.state.fullScreen ? PREVIEW_CAMERA_W : PREVIEW_CAMERA_H
            let y = this.state.fullScreen ? kw - yHeight - PREVIEW_PADDING : kw - GESTURE_Y;
            console.log("onPanResponderRelease2",y);

            this.setState({ minY: y });
          }

        } else {

          let sideX = kw / 2 - 62;
          if (this.state.minX <= sideX) {
            this.setState({ minX: PREVIEW_PADDING });
          } else {
            this.setState({ minX: kw - GESTURE_X });
          }
        }

      },
      onPanResponderTerminate: (evt, gestureState) => {
        // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
        console.log("======+====++++++++++++++++++++++++++++++++++++++");
      }
    });
  }

  _setIndexOfResolution(resolution) {
    // if (Platform.OS == "ios") {
    let index = 0;
    switch (resolution) {
      case 1:
        index = 1;
        break;
      case 2:
        index = 2;
        break;
      case 3:
        if (VersionUtil.Model_chuangmi_051a01 == Device.model
          || CameraConfig.Model_chuangmi_069a01 == Device.model) {
          index = 3;
        } else {
          index = 2;
        }
        break;
      default:
        index = 0;
        break;
    }
    this.selectedIndexArray = [index];
    // }
  }

  _setStatusBarForNativeView() {
    // this.isPageForeGround = false;

    this.state.darkMode ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
  }

  _orientationListener = (orientation) => {
    console.log("====+========+====+==+=+=++=+=+=+=+=+=+====",orientation,this.mOri)
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround || !this.isAppForeround) {

      return;
    }
    LogUtil.logOnAll(TAG, `device orientation changed :${ orientation } want ${ this.mOri } current orientation: ${ this.state.fullScreen }`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      // 如果是ios pad仅仅旋转屏幕，动态再去获取一次
      if (Host.isPad && !this.state.fullScreen){
        console.log('=========this is do');
        setTimeout(() => {
          this._initHeightValues();
          this.setState({});
        },300);
      }
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') { // 
        // do something with landscape layout
        let shouldChangeCallStatus = this.state.fullScreen == false;// 不是全屏，才会切换call状态
        // if (Host.isPad) {
        //   Service.miotcamera.enterFullscreenForPad(true);
        // }
        this.setState(() => {
          return { fullScreen: true, showPlayToolBar: true };
        }, () => {
          // pad切换全屏，导致宽度高度都会改变，这个里需要重新计算
          if (Host.isPad && !this.padScreenHeightHorizontal) {
            this.getPadHorizontalInfo && clearTimeout(this.getPadHorizontalInfo);
            this.getPadHorizontalInfo = setTimeout(() => {
              if (Host.isAndroid) {
                Host.getPhoneScreenInfo()
                  .then((result) => {
                    this.padScreenHeightHorizontal = result.viewHeight;
                    this.padScreenWidthHorizontal = result.viewWidth;
                    console.log("pad size:",kScreenHeight,kWindowHeight,kWindowWidth,this.padScreenHeightHorizontal,this.padScreenWidthHorizontal);
                  })
                  .catch(() => {});
              }
            },  400); //部分Android平板需要更长的延时才能获取到横屏后的宽高
          }
          // 切换到横屏了，判断是否是calling中，是的话，就先关后开 目的主要是处理竖屏切换横屏，动画不动的问题
          if (!shouldChangeCallStatus) {
            return;
          }
          this._restoreCallingIconAnimation();
        });

      } else {
        let shouldChangeCallStatus = this.state.fullScreen == true;// 是全屏，才会切换call状态
        // do something with portrait layout
        // if (Host.isPad) { // 直播页面也需要调用
        //   Service.miotcamera.enterFullscreenForPad(false);
        // }
        this.setState(() => {
          return { fullScreen: false };
        }, () => {
          if (!shouldChangeCallStatus) {
            return;
          }
          this._restoreCallingIconAnimation();
        });
      }

      this.setState({ isWhiteVideoBackground: orientation === "LANDSCAPE" ? false : true });
    } else {
      // ios need restore for next lock
      this.restoreOri(4);
    }
  };

  _restoreCallingIconAnimation() {
    if (this.state.isCalling) {
      this.setState(() => {
        return { isCalling: false };
      }, () => {
        this.setState({ isCalling: true });
      });
    }
  }

  _fetchFirmwareUpdateInfo() {
    Service.smarthome.getFirmwareUpdateInfo(Device.deviceID)
      .then((res) => {
        console.log("get firmware info: ", res);
        if (Device.isOwner) {
          this.setState({ showRedDot: res.needUpgrade });
        }
        if (res.upgrading && res.upgrading == true) {
          this.isFirmwareUpdating = true;
          this.setState({ showErrorView: true, errTextString: LocalizedStrings["firmware_updating_desc"] });
          this._clearTimer(this.mFirmwareUpdatingTimer);
          this.mFirmwareUpdatingTimer = setTimeout(() => {
            this._fetchFirmwareUpdateInfo();
          }, 5000);
        } else if (this.isFirmwareUpdating && res.upgrading == false) {
          this.isFirmwareUpdating = false;
          this.setState({ showErrorView: false });
          CameraConfig.checkNasVersion = true;
          this._onResume();
        }
      })
      .catch((err) => {
        console.log(err);
        this.setState({ showRedDot: false });
      });
  }

  _waterTimeChangeHandler = (value) => {
    console.log("====================water",value);
    if (value != undefined) {
      this.setState({ isWatermarkEnable: value });
    }
  }

  _powerOffHandler = (isPowerOn, popSleepDialog, shouldStartVideo = false) => {
    // 远端的下发的状态与本地的状态不一致 就回走到这里
    if (!isPowerOn) { // 变成了休眠
      // 如果检测到了休眠，走设备端挂断流程
      Toast.success("device_hangup");
      // 表示设备端挂断了通话
      if (this.state.fullScreen) {
        this.restoreOri(5);
      }
      this._stopAll();
      this._doGoBack();
    } else { // 唤醒了
      this.isClickSleep = false;
      this.setState({ isSleep: false, showPlayToolBar: true, showPoweroffView: false });
      Service.smarthome.reportLog(Device.model, "on wake up");
      this.isPowerOn = true;
      CameraPlayer.getInstance().setPowerState(true);
      if (shouldStartVideo) {
        this._refreshRemoteProps();
        this._toggleAudio(CameraConfig.getUnitMute());
        this.queryNetworkJob();
      }
    }
  };

  loadLocalSetttings() {

    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == "" || res == null) {
        res = false;
        StorageKeys.IS_VIP_STATUS = false;
      }
      this.isVip = res;
      CameraConfig.isVip = res;
    })
      .catch((err) => {
        StorageKeys.IS_VIP_STATUS = false;
      });

    StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => { // 是否使用畸变纠正
      if (typeof (res) === "string" || res == null) {
        if (CameraConfig.isXiaomiCamera(Device.model) && !CameraConfig.Model_chuangmi_051a01 == Device.model) {
          res = false;// 默认禁用畸变纠正
          StorageKeys.IS_LENS_DISTORTION_CORREECTION = false;
        } else {
          res = true;// 默认启用畸变纠正
          StorageKeys.IS_LENS_DISTORTION_CORREECTION = true;
        }

      }
      this.setState({ useLenCorrent: res });
    }).catch((error) => {
      console.log(error);
      this.setState({ useLenCorrent: true });
    });
    StorageKeys.IS_PTZ_ROTATION_ENABLE.then((res) => { // 云台手势转动是否开启
      if (res == null || typeof (res) != 'boolean') {
        res = true;// 默认启用云台手势转动是否开启
        StorageKeys.IS_PTZ_ROTATION_ENABLE = true;
      }
      this.enablePtzRotation = res;
    }).catch((error) => {
      console.log(error);
      this.enablePtzRotation = true;
    });

    StorageKeys.IS_SHOW_DIRECTION_VIEW.then((res) => { // 是否显示方向盘
      if (res === true || res === '' || res === null) { // 当第一次进入res还没有值,ios和安卓表现形式不一样 ios是null 安卓是''
        this._showDirectionViewAnimated(true);
      }
    }).catch((error) => {
      StorageKeys.IS_SHOW_DIRECTION_VIEW = false;
    });

    StorageKeys.IS_SHOW_SDCARD_PAGE.then((res) => {
      if (res === "" || res == null) {
        StorageKeys.IS_SHOW_SDCARD_PAGE = false;
        res = false;
      }
      this.showSdcardPage = res;
    });

    StorageKeys.IS_IMAGE_FLIP.then((isFlipOn) => {
      StorageKeys.IMAGE_ROTATION.then((imgRotation) => {
        this.setState({
          isFlip: isFlipOn,
          rotation: imgRotation
        });
      });
    }).catch((err) => {
      console.log(err);
    });

    StorageKeys.IS_AI_FRAME_OPEN.then((res) => {
      if (res === true) {
        this.setState({ enableAIFrame: true });
      } else {
        this.setState({ enableAIFrame: false });
      }
    });

    this._fetchFirmwareUpdateInfo();
  }

  checkIsInternationalServer() {
    Service.getServerName().then((server) => {
      CameraConfig.setIsIndiaServer(false); // 重置
      CameraConfig.setIsInternationalServer(true); // 重置

      let countryCode = server.countryCode;// countryCode是大写
      let serverCode = server.serverCode;
      CameraConfig.updateCloudSupportCountry(serverCode.toUpperCase());
      const cloudServer = ['us', 'de', 'sg'];
      if (serverCode.toLowerCase() == "cn" || GermanCluster.includes(serverCode.toUpperCase()) || cloudServer.includes(serverCode)) {
        CameraConfig.setSupportCloudCountry(true);
      }
      if (countryCode.toLowerCase() === "cn") {

        this.setState({ isInternationServer: false });
        CameraConfig.setIsInternationalServer(false);
        let language = Host.locale.language || "en";
        if (language == "zh" && !Device.isReadonlyShared) {
          this._getOperationBanner();
        }
        return;
      }
      this.isCloudServer = cloudServer.includes(serverCode);
      CameraConfig.setIsCloudServer(serverCode);// 该服务是否有海外云存功能
      // 如果是欧洲服务器
      this.isEuropeServer = CameraConfig.getIsEuropeServer();
      this.setState({ isInternationServer: true });
      CameraConfig.setIsInternationalServer(true);
      if (countryCode.toLowerCase() == "in") {
        CameraConfig.setIsIndiaServer(true);
      } else {
        CameraConfig.setIsIndiaServer(false);
      }

      this._getSecurityCodePush();

    }).catch((err) => { // 
      console.log(err);
      this.setState({ isInternationServer: true });
      CameraConfig.setIsInternationalServer(true);
    });
  }

  checkPrivacyDialog() {
    // if (Device.model == CameraConfig.Model_chuangmi_021) {
    this.checkPrivacyDialogLocal();
    // } else {
    //   this.checkPrivacyOnline();
    // }
  }

  checkPrivacyDialogLocal() {
    //因为只会执行一次，所以如果是新设备绑定进来的，就当做没有
    TrackConnectionHelper.onPrivacyBeginCheck();
    this.globalLoadingTimeout = setTimeout(() => {
      this.setState({ showGlobalLoading: true });
    }, 500);
    this.privacySDKUtil.bindPrivacyCallback(this);
    this.privacySDKUtil.checkNeedPopPrivacyDialog();
  }


  userExpPlanPopupCallBack(state) {
    LogUtil.logOnAll("userExpPlanPopupCallBack-=-=======", state);
    if (state) {
      this._refreshRemoteProps();
    }
  }

  progressPrivacyAuthed() {
    // 在这里重新刷一遍吧。。。
    Service.getServerName()
      .then((server) => {
        let countryCode = server.countryCode;// countryCode是大写
        let serverCode = server.serverCode;
        LogUtil.logOnAll("initPage getServerName:" + JSON.stringify(countryCode));
        if (countryCode.toLowerCase() === "cn") {
          CameraConfig.setIsInternationalServer(false);
          this.needCheckUserExp = true;
        }

        if (this.privacyDialogPoped && this.needCheckUserExp) { // 大陆 && 隐私弹框出现了 代表会有用户体验计划出现，再等等。
          return;
        }
        this._refreshRemoteProps();
      }).catch((err) => {
      LogUtil.logOnAll("progressPrivacyAuthed getServerName err=", JSON.stringify(err));
      this.needCheckUserExp = false;
      this._refreshRemoteProps();
    });

  }

  // 授权了 进行后面的步骤
  onPrivacyAuthed() {
    Service.smarthome.reportLog(Device.model, "camera device auth success, start to connect");
    this.progressPrivacyAuthed();
    LogUtil.logOnAll("privacy authed");
    TrackConnectionHelper.onPrivacyEndCheck();
    clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false });
    this.showLoadingView();
    this.queryPowerOffProperty();// 查询 remote的网络状态。
    Service.smarthome.reportLog(Device.model, "authed, go connect");
    this.isPrivacyAuthed = true;
    this.queryNetworkJob();// 这里开始连接摄像头。
    //授权通过后，开始处理韩国强制密码功能
    PinCodeUtil.queryShouldPopPinCodeSeting()
      .then((result) => {
        LogUtil.logOnAll("PincodeUtil", "should pop privacy dialog???:" + result);

        if (result) { // 需要处理韩国强制密码需求
          this.isOnRequestingPincode = true;
          Host.ui.openSecuritySetting();// 打开页面
          this._bindPinCodeSwitchChangedEvent();
        } else {
          // 不需要处理韩国强制密码需求
          // this.checkPrivacyDialog();
          // checkPrivacyDialog 刚进来就执行。
        }
      })
      .catch((err) => {
        console.log(err, err.stack);
        // this.checkPrivacyDialog();// 请求出错，就当做没毛病吧
      });
    SpecUtil.queryWatermark()
      .then((res) => {
        this.setState({ isWatermarkEnable: res });
      });
  }

  // 授权拒绝了
  onPrivacyReject() {
    TrackConnectionHelper.onPrivacyEndCheck();
    TrackConnectionHelper.report();

    Package.exit();//授权拒绝了；
  }

  // 隐私弹框出现了，让loading 消失
  onPrivacyDialogPoped() {
    this.privacyDialogPoped = true;
    clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false });// 隐私对话框弹出来的时候应该隐藏全局的loading
  }

  // 从服务器是否授权状态拉取超时, loading消失，弹出退出对话框
  onPrivacyTimeout() {
    //这里又应该弹一个不可取消的对话框。 点确认 就退出插件。
    clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false, showTimeoutDialog: true });
  }


  _onResume() {
    console.log(TAG, "_onResume");
    // this.fromOneKeyCall = this.props.navigation.getParam("fromOneKeyCall");
    // this.startCallFlag = this.props.navigation.getParam("startCallFlag");
    // LogUtil.logOnAll(TAG, `_onResume fromOneKeyCall=${ this.fromOneKeyCall } startCallFlag=${ this.startCallFlag }`);

    this.restoreOri(5);

    clearTimeout(this.delayPauseTimer);
    let colorScheme = DarkMode.getColorScheme();

    if (colorScheme == 'dark') {
      this.setState({ darkMode: true });
    } else {
      this.setState({ darkMode: false });
    }

    CameraPlayer.getInstance().bindOneKeyCallReceived(this._receivedOneKeyCall.bind(this));
    CameraPlayer.getInstance().bindCallStateReceived(this._receivedCallStateChange.bind(this));
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindWaterTimeCallback(this._waterTimeChangeHandler);
    CameraPlayer.getInstance().bindPauseAllCallback(() => {
      this._stopAll(false, false);
    });

    // this._getOneKeyCallStatusWithoutToast();
    if (CameraConfig.isToUpdateVipStatue) {
      this._fetchVipStatus();
      CameraConfig.isToUpdateVipStatue = false;
    }
    this._loadWaterMarkInfoFromCache();
    this.enterLiveVideoTime = new Date().getTime();
    this._checkNasVersion();

    if (this.isFirstEnter) {
      return;// 刚进来的时候不请求connect  避免出现问题
    }
    if (!this.isPageForeGround) {
      return;
    }

    if (!this.isPluginForeGround) {
      return;
    }
    // 之前在render里设置的颜色，从设置里退回，如果摄像头处于关闭状态 render不会掉用。
    // StatusBar.setBarStyle('light-content');
    this.loadingRetryTimer = 0;
    if (this.loadingTooLongTimer) {
      clearTimeout(this.loadingTooLongTimer);
    }
    this.loadingTooLongTimer = setTimeout(() => {
      if (this.loadingRetryTimer != 0 && (new Date().getTime() - this.loadingRetryTimer) > 5000) {
        // ???这是什么逻辑
        Service.smarthome.reportLog(Device.model, "loadingTooLongTimer");
        this.queryNetworkJob();
        console.log("connect retry for loadingTooLongTimer");
      }
    }, 8000);

    this.loadLocalSetttings();


    // first query is power off or not
    let isPowerOn = CameraPlayer.getInstance().getPowerState();
    LogUtil.logOnAll(TAG,"power",isPowerOn,this.isPowerOn);
    if (this.isPowerOn != isPowerOn) { // 去往其他页面的时候power off了
      this._powerOffHandler(isPowerOn, false, true);
      return;
    }


    if (!this.isPowerOn) { // 电源g关了  没有必要往下面走.
      return;
    }

    this._getKanjiaSetting();


    // setTimeout(() => {
    //   if (this.cameraGLView == null) {
    //     return;
    //   }
    //
    //   Service.smarthome.reportLog(Device.model, "onresume, go connect");
    //   // 这里直接走连接的步骤吧  出现错误也会提示的
    //   this.queryNetworkJob();// 这里会处理是否连接成功之类的逻辑
    //   console.log("on resume");
    //
    // }, 500);

    // this._toggleAudio(CameraConfig.getUnitMute());
  }

  _onPause() {
    console.log("===========================onPause")
    this.cruiseTimer && clearTimeout(this.cruiseTimer);
    if (this.isOnRequestingPincode) {
      console.log("正在请求密码，不执行onpause。");
      return;
    }
    if (this.isCheckingPermission) {
      console.log("======+========+========request ")
      return;
    }
    // this._setStatusBarForNativeView();//把状态栏字体变成黑色，

    if (this.cameraGLView != null && !this.destroyed) {
      this.cameraGLView.stopRender();// stopRender
    }

    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindPauseAllCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    clearInterval(this.logInterval);


    // save state
    StorageKeys.IS_SHOW_DIRECTION_VIEW = this.state.showDirectCtr;

    if (this.showPlayToolBarTimer) {
      clearTimeout(this.showPlayToolBarTimer);
      this.showPlayToolBarTimer = null;
    }

    if (this.enterLiveVideoTime > 0) { // _onPause会被调多次
      let liveVideoTime = (new Date().getTime() - this.enterLiveVideoTime) / 1000;
      TrackUtil.reportResultEvent("Camera_Play_Time", "Time", liveVideoTime); // Camera_Play_Time
      this.enterLiveVideoTime = 0;
    }

    if (!this.isPowerOn) {
      return;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (this.cameraGLView == null) {
      return;
    }

    if (Platform.OS === "android") {
      this.setState({ whiteTitleBg: true });
    }
    if (!this.isDeviceHangup) {
      Toast.success("user_hangup");
    }

    this._stopAll(this.state.showPauseView, false); // 要离开这个页面，停止设备发送音频，但不修改unitMute状态。

    if (this.loadingTooLongTimer) {
      clearTimeout(this.loadingTooLongTimer);
    }
    this._doGoBack();
  }


  _stopAll(showPauseView = false, setUnitMute = true, needSetPlayToolBar = true) {
    console.log("=====+===+==+==+=====stopAll");
    this._stopRecord();
    if(!this.isStartCallError) {
      console.log("=====+===+==+==+=====stopCall");
      //  isAudioCallInCommunication 要在挂断之前
      this.setState({isAudioCallInCommunication:false},()=>{
        this._stopCall();
      })
     
    }

    this._toggleAudio(true, setUnitMute);
    CameraPlayer.getInstance().stopVideoPlay();
    if (this.cameraGLView != null && !this.destroyed) {
      this.cameraGLView.stopRender();// stopRender
    }
    this.hideBgBpsCount = 0;
    if (needSetPlayToolBar) {
      this.setState({ showPauseView: showPauseView, showLoadingView: false, showPlayToolBar: false });
    } else {
      this.setState({ showPauseView: showPauseView, showLoadingView: false });
    }
  }

  queryNetworkJob() {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPrivacyAuthed) {
      return;
    }
    console.log("=++++++++++==+==+=+=+====+=+==+=+=+=====+==")
    Service.smarthome.reportLog(Device.model, "start query network");
    TrackConnectionHelper.onNetworkCheck();
    this.callIsStop = false;
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        Service.smarthome.reportLog(Device.model, "查询网络结果：当前网络类型" + state + " pauseOnCellular" + pauseOnCellular);
        if (state == "NONE" || state == "UNKNOWN") {
          this.currentNetworkState = 0;
          // this.setState({ showErrorView: true });
          this._handleNetworkError();
          return;
        }
        TrackConnectionHelper.onNetworkChecked();
        this.isPhoneNetworkError = false;
        this.showLoadingView();
        this.setState({ showPauseView: false });
        Service.smarthome.reportLog(Device.model, "start query network success:" + state);
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch((err) => { // 获取网络状态失败 也直接走开始连接的流程
        TrackConnectionHelper.onNetworkChecked();
        console.log(err);
        Service.smarthome.reportLog(Device.model, `start query network error${ JSON.stringify(err) }`);
        this._startConnect();// 开始连接
      });
  }

  restoreOri(from = -1) {
    console.log(TAG, "restoreOri from:", from,this.mOri);
    Service.smarthome.reportLog(Device.model, `restoreOri from ${ from }`);
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait(1);
    } else {
      this.toLandscape(1);
    }
  }


  toPortrait(from = -1) {
    StatusBar.setHidden(false);
    Service.smarthome.reportLog(Device.model, `toPortrait from ${ from }`);
    console.log(TAG, "toPortrait from:", from);
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
    if (Platform.OS == "android" && this.evenLockScreen > 0) {
      this.setState({ restoreOriFinished: true });
    }
    this._hidePlayToolBarLater();
    clearTimeout(this.angleViewTimeout);
    this.setState({ showCameraAngleView: false, angleViewShowScale: false });
  }

  toLandscape(from = -1) {
    StatusBar.setHidden(true);
    console.log(TAG, "toLandscape from:", from);
    Service.smarthome.reportLog(Device.model, `toLandscape from ${ from }`);
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
    if (Platform.OS == "android" && this.evenLockScreen > 0) {
      this.setState({ restoreOriFinished: true });
    }
    if (this.showPlayToolBarTimer) {
      clearTimeout(this.showPlayToolBarTimer);
      this.showPlayToolBarTimer = null;
    }
    if (Host.isPad) {
      Service.miotcamera.enterFullscreenForPad(true);
    }
    //

    this._hidePlayToolBarLater();
    clearTimeout(this.angleViewTimeout);
    this.setState({ showCameraAngleView: false, angleViewShowScale: false });
  }

  _connectionHandler = (connectionState) => {
    this.loadingRetryTimer = 0;
    Service.smarthome.reportLog(Device.model, `why!, _connectionHandler, connectionState.state: ${ connectionState.state }`);
    if (connectionState.state == MISSConnectState.MISS_Connection_ReceivedFirstFrame) {
      this.connectTimeout && clearTimeout(this.connectTimeout);
      if (this.hasFirstFrame || Platform.OS === "ios") {
        this.setState({ showDefaultBgView: false, whiteTitleBg: false, showLoadingView: false, showPlayToolBar: true });
      } else {
      }
      if (!Host.isAndroid) {
          StorageKeys.VIDEO_SCALE.then((result) => {
            this.setVideoScale();
          }).catch((err) => {
            this.setVideoScale();
          });
        setTimeout(() => {
          AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip);
        }, 100);
      }

      TrackConnectionHelper.onIFrameReceived();
    }

    if (this.state.pstate == connectionState.state && this.state.error == connectionState.error) {
      return;// 状态一样 没有必要通知
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Disconnected) {
      this.isConnecting = false;
      if (this.state.showPauseView) { // 如果显示暂停，就不显示error
        return;
      }
      TrackConnectionHelper.onDisconnected();
      if (this.state.isSleep) {
        // 休眠状态下断开了连接，也不显示errorView
        return;
      }

      /**
       * @Author: byh
       * @Date: 2024/6/19
       * @explanation:
       * 需求变更 断开连接后补显示错误，一直加载，30s后，挂断通话
       *********************************************************/
      if (this.isFirstErrorEnter) {
        // Toast.success('network_opposite_wrong');
        this.isFirstErrorEnter = false;
        this.connectTimeout && clearTimeout(this.connectTimeout);
        this.connectTimeout = setTimeout(() => {

          CameraPlayer.getInstance().videoCallState = 0;
          // 挂断通话 结束当前页面
          if (this.props.navigation.getParam("ori") !== "LANDSCAPE") {
            this.toPortrait(5);
          }
          Toast.success('one_key_call_talkend');
          this._stopAll();
          this._doGoBack();
        },30000);
      }
      this.isErrorToConnect = true;
      // 一直重试
      setTimeout(() => {
        Service.smarthome.reportLog(Device.model, `error retry connect: ${ this.connRetry }`);
        console.log("connection retry");
        this.isCallStart = false;
        this.queryNetworkJob();
      }, 300);

      // if (this.connRetry > 0 && this.state.pstate != connectionState.state) {
      //   this.connRetry = this.connRetry - 1;
      //   setTimeout(() => {
      //     Service.smarthome.reportLog(Device.model, `error retry connect: ${ this.connRetry }`);
      //     console.log("connection retry");
      //     this.queryNetworkJob();
      //   }, 300);
      //   return;
      // }
      // this.handleDisconnected(connectionState.error);
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Connected) { // onconnected 发送video-start
      this.loadingRetryTimer = new Date().getTime();
      // 移除定时器
      this.connectTimeout && clearTimeout(this.connectTimeout);
      if (!this.isConnecting) {
        return;
      }
      this._sendDirectionCmd(DirectionViewConstant.CMD_GET);
      this.isConnecting = false;
      this.startVideoRetry = false;
      console.log("start send video start");
      this._realStartVideo();

      TrackConnectionHelper.onConnected();
      this._refreshRemoteProps();
      // this._refreshRemoteProps();
      this.delayGetPreSetPosition = setTimeout(() => {
        this._getPreSetPosition();
      }, 1000);
    }
    if (connectionState.state == MISSConnectState.MISS_Connection_Connecting) {
      this.isConnecting = true;
    }
    if (connectionState.state >= MISSConnectState.MISS_Connection_Connected) {
      Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
      this.setState({ showErrorView: false });
      Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);
    }

    if (connectionState.state >= MISSConnectState.MISS_Connection_ReceivedFirstFrame) {
      this.isFirstErrorEnter = true;
      this.connectTimeout && clearTimeout(this.connectTimeout);
      this.connRetry = 2;
      if (this.isPtz) {
        setTimeout(() => {
          this._getRotateAngle();
        }, 500);
      }

      if (!Host.isAndroid) { // android收到关键帧没有用，要等到解码出来的firstVideoShow， ios没有这个事件，只能在收到i帧后调用
        setTimeout(() => {
          AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip);
        }, 100);

      }

      this.hasFirstFrame = true;
      if (this.showPlayToolBarTimer) {
        clearTimeout(this.showPlayToolBarTimer);
        this.showPlayToolBarTimer = null;
      }
      if (!this.state.fullScreen) {
        this._hidePlayToolBarLater();
      }

    }
    this.setState({
      pstate: connectionState.state,
      error: connectionState.error
    });
  };

  handleDisconnected(errorCode) { // 已经断开了连接。
    console.log("disconnected");
    this._stopRecord();
    let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${ LocalizedStrings["camera_connect_error"] } ${ errorCode }, ${ LocalizedStrings["camera_connect_retry"] }`);
    this.setState({
      showPlayToolBar: false,
      isRecording: false,
      isCalling: false,
      showLoadingView: false,
      showPauseView: false,
      showPoweroffView: false,
      showErrorView: true,
      errTextString: errorStr
    });
    this.isClickCall = false;
    // this._toggleAudio(true);
    if (this.cameraGLView != null && !this.destroyed) {
      this.cameraGLView.stopAudioPlay();
      // sync ui state
      this.setState({ isMute: true });
      this.cameraGLView.stopAudioRecord();
      // this._stopCall();
      // CameraPlayer.getInstance().stopVideoPlay();
      this.cameraGLView.stopRender();// stopRender
    }

    if (!Device.isOnline) {
      return;
    }
  }

  _getRotateAngle() {
    LogUtil.logOnAll(TAG, "全景图成功后，发起查询角度命令");

    if (CameraConfig.isXiaomiCamera(Device.model)) {
      let param = { operation: 6 };
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_MOTOR_REQ, param)
        .then(() => {
          this.isSendingRdtCmd = true;
        })
        .catch((error) => {
          LogUtil.logOnAll(TAG, "查询角度命令发送失败:" + JSON.stringify(error));
          this.isSendingRdtCmd = false;
        });
    } else {
      let GetCurrentRotateAngle = 14;// 获取当前摄像机坐标(角度）
      let command = GetCurrentRotateAngle;
      let params = `${ "{'mac':'F1F2F3F4F5F6','panorama':''}" }`;// 小米保留，固件暂时不做解析

      // 1个byte占用8个bit
      // 1个int占用4个byte
      let buf = new ArrayBuffer(8);// 1个int占用4个byte；

      let data = new Uint32Array(buf);
      data[0] = command;// 放命令号
      data[1] = 4;// 放 data的byte长度 这里是32位整数 长度是4
      let data_byte = new Uint8Array(buf);// int转byte

      console.log("_getRotateAngle data=", data);

      let base64Data = base64js.fromByteArray(data_byte);

      Service.miotcamera.sendRDTCommandToDevice(base64Data).then((res) => {
        this.isSendingRdtCmd = true;
        console.log("_getRotateAngle command=", command, ",res=", res);
      }).catch((err) => {
        this.isSendingRdtCmd = false;
        console.log("_getRotateAngle res=", err);
      });
    }

  }

  bindRdtListener() {
    this.rdtListener = DeviceEventEmitter.addListener(kRDTDataReceiveCallBackName, ({ data }) => {
      if (!this.isPtz) {
        return;
      }
      if (!this.isSendingRdtCmd) {
        return;// 不是这边发送的，不管。
      }
      // console.log("rdt listener received data=", data);

      if (data == null || data.length <= 0) {
        return;
      }
      try {
        let result = null;
        try {
          result = base64js.toByteArray(data);// uint8array
        } catch (exception) {
          console.log("rdt data  error");
          console.log(exception);
          console.log(data);
          throw exception;
        }

        // console.log("rdt result: " + result);

        let command = NumberUtil.byteArrayToInt(result, 0);
        if (command <= 0 || command > 65535) {
          throw ("invalid command");
        }

        let size = NumberUtil.byteArrayToInt(result, 4);
        if (size <= 0 || size >= 20971520) {
          throw ("invalid size");
        }
        let rawSize = size;


        // let status = this.byteArrayToInt(result, 8)
        // let currentPacketDataSize = result.length - 12;

        let commandBuffer = new Uint8Array(rawSize);
        commandBuffer.set(result.slice(12));

        // parse commandBuffer
        // PTZCMISSRdtSetPanoramaRotateAngle = 13, //点击全景图转动电机
        // PTZCMISSRdtGetCurrentRotateAngle = 14 //获取当前摄像机坐标(角度）

        if (command === 14) {
          let data = new Uint8Array(commandBuffer);
          NumberUtil.byteArrayToInt(data, 0);

          let positionX = NumberUtil.byte2ToUnsignedShort(data, 0);
          let positionY = NumberUtil.byte2ToUnsignedShort(data, 2);

          console.log("rdt listener update positionX: ", positionX);
          console.log("rdt listener update positionY: ", positionY);

          this.setState({ angle: positionX, elevation: positionY });
          if (this.showPanoAfterReceivedRotateAngle) {
            this.setState({ panoViewStatus: 3 });
            this.showPanoAfterReceivedRotateAngle = false;
            if (this.showPanoToastAfterReceivedRotateAngle) {
              Toast.success("pano_view_success");
              this.showPanoToastAfterReceivedRotateAngle = false;
            }
          }
          // if(this.state.showPanoView){
          //   this.setState({panoViewStatus:3, angle: positionX, elevation: positionY})
          // }
          this.isSendingRdtCmd = false;// end

        } else if (command === 13) {
          console.log("rdtListener SetPanoramaRotateAngle success!");
        }
      } catch (err) {
        // console.log(`rdt data error:${ err }`);
        this.isSendingRdtCmd = false;// end
      }
    });

    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
  }

  // 这里切换角度
  _p2pCommandHandler = ({ command, data }) => {
    console.log("p2pCommandHandler", "command",command);
    // 扩展程序注册命令回复回调，command为返回的命令号值，data 为P2P命令的返回数据。
    if (command == MISSCommand.MISS_CMD_SPEAKER_START_RESP) {
      if (this.forceSleep || this.state.showErrorView) {
        return;// 已经休眠或者显示了错误文案，就不打开麦克风
      }
      // 有通道回就需要移除这个定时
      this.startCallAgainTimeout && clearTimeout(this.startCallAgainTimeout);
      this.callErrorSigal += 1;
      this.isClickCall = false;
      this.startSpeakerTime = new Date().getTime();

      let ba = base64js.toByteArray(data);
      LogUtil.logOnAll(TAG,'receive start speaker:'+ba);
      if (ba.length > 0) {
        console.log('receive start speaker 0');
        console.log(ba[0]);
        if (Platform.OS === 'android') {
          if (ba[0] == 48) {
            if (this.callType == 0 || this.callType == 2) {
              // 下发视频通话指令
              this._startVideoCommand();
            }
            console.log("start call in android");
            this._toggleAudio(false);
            // 麦克风进来，通道打开，是否需要开启录音，视情况而定
            if (this.state.audioOpen) {
              if (this.cameraGLView != null && !this.destroyed) {
                LogUtil.logOnAll(TAG,"start audio record");
                this.cameraGLView.startAudioRecord();
              }
            }

            this.setState({ isCalling: true, isCallPrepare: false, showCallDuration: true, showOneKeyCallDialog: false });
            this._startCallTimeInterval();
            Service.miotcamera.sendP2PCommandToDevice(MISSCommand_ECO.MISS_CMD_MIC_OPEN, {}).then((res) => console.log("CMD MIC SUCCESS")).catch((err) => console.log("CMD MIC ERROR", err));
            // AlarmUtilV2.putOneKeyCallStatus(2).then((res) => {
            //   LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            // }).catch((err) => {
            //   LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            // });
            return;
          }
        } else {
          if (ba[0] == 0) {
            if (this.callType == 0 || this.callType == 2) {
              // 下发视频通话指令
              this._startVideoCommand();
            }
            this._toggleAudio(false);
            let audioIsOpen = false;
            if (this.state.audioOpen) {
              audioIsOpen = true;
              this.callTimeout && clearTimeout(this.callTimeout);
              this.callTimeout = setTimeout(() => {
                if (this.cameraGLView != null && !this.destroyed && this.state.audioOpen) {
                  this.cameraGLView.startAudioRecord();
                }
              }, 800);// temp solution for bug MIIO-42838
            }

            this.setState({ isCalling: true, isCallPrepare: false, showCallDuration: true, showOneKeyCallDialog: false });
            this.callTimeTimeout && clearTimeout(this.callTimeTimeout);
            this.callTimeTimeout = setTimeout(() => {
              this._startCallTimeInterval();
            }, 500);

            Service.miotcamera.sendP2PCommandToDevice(MISSCommand_ECO.MISS_CMD_MIC_OPEN, {}).then((res) => console.log("CMD MIC SUCCESS")).catch((err) => console.log("CMD MIC ERROR", err));
            // AlarmUtilV2.putOneKeyCallStatus(2).then((res) => {
            //   LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            // }).catch((err) => {
            //   LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            // });
            console.log("this.iscalling = true");
            return;
          }
        }
      }
      LogUtil.logOnAll("speak_failed because =", data);
      // Toast.fail("speak_failed");
      this.setState({ isCallPrepare: false });
      this.audioCMDError = true;
      this.callErrorCheckForBack();

    // } else if (command == MISSCommand.MISS_CMD_VIDEOCALL_START_RESP) {
    } else if (command == 281) {
      // 视频通话miss指令下发后的响应
      this.startSpeakerTime = new Date().getTime();
      this.callErrorSigal += 1;
      let ba = base64js.toByteArray(data);
      console.log(' receive start video call speaker',ba);
      if (ba.length > 0) {
        console.log('receive start video call 0');
        console.log(ba[0]);
        if (Platform.OS === 'android') {
          if (ba[0] == 0) {
            console.log("start call video in android");
            this.setState({ videoOpen: true, isCalling: true, isCallPrepare: false, showOneKeyCallDialog: false },()=>{

              this.cameraCaptureView && UIManager.dispatchViewManagerCommand(
                  findNodeHandle(this.cameraCaptureView),
                  "startCameraCapture",
                  [],
                );
            });
            return;
          }
        } else {
          if (ba[0] == 0) {
            this.setState({ videoOpen: true, isCalling: true, isCallPrepare: false, showOneKeyCallDialog: false }, () => {
              // 开启相机预览
              this.cameraCaptureView && NativeModules.MHCameraCaptureViewManager.startCameraCapture(findNodeHandle(this.cameraCaptureView));
            });
            return;
          }
        }
      }
      LogUtil.logOnAll("video_failed because =", data);
      // Toast.fail("speak_failed");
      this.setState({ isCallPrepare: false });
      this.videoCMDError = true;
      this.callErrorCheckForBack();
    } else if (command == MISSCommand.MISS_CMD_MOTOR_RESP) {
      console.log("received p2p angle resp");
      console.log(data); // {"ret":0,"angle":12,"elevation":1}
      if (this.isSendingRdtCmd) {
        LogUtil.logOnAll(TAG, "查询电机角度命令返回了：" + JSON.stringify(data));
      }
      try {
        if (typeof (data) == 'string') {
          data = JSON.parse(data);
        }
        this.angleData = data;

        let angleValue = Number(data.angle);
        let elevationValue = Number(data.elevation);
        let result = Number(data.ret);

        if (typeof (angleValue) == 'number' && typeof (elevationValue) == 'number') {
          if (this.ctrlCurrentLocation[0] != 0) {
            console.log(`preSetPosition update img current:${ angleValue } - ${ elevationValue } == h-v : ${ this.ctrlCurrentLocation[1] } - ${ this.ctrlCurrentLocation[2] }`);
            if ((angleValue > this.ctrlCurrentLocation[1] - 2 && angleValue < this.ctrlCurrentLocation[1] + 2) &&
              (elevationValue > this.ctrlCurrentLocation[2] - 2 && elevationValue < this.ctrlCurrentLocation[2] + 2)) {
              console.log(`preSetPosition update img h-v : ${ this.ctrlCurrentLocation[1] } - ${ this.ctrlCurrentLocation[2] }`);
              let imgPath = `${ this.preSetPositionImg }${ this.ctrlCurrentLocation[0] }.jpg`;
              this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
              this.delayUpdatePreSetImg = setTimeout(() => {
                this._updatePreSetPositionImg(imgPath, this.ctrlCurrentLocation[0]);
                this.ctrlCurrentLocation[0] = 0;
              }, 2000);
            }
          }
          // if (this.angleViewTimeout) {
          //   clearTimeout(this.angleViewTimeout)
          //   this.angleViewTimeout = null
          // }
          // this.angleViewTimeout = setTimeout(() => {
          //   this.setState({ showCameraAngleView: false, angleViewShowScale: false });
          // }, 3000);
          // this.setState({showCameraAngleView: true, angleViewShowScale: false, angle: angleValue, elevation: elevationValue})
          // angle : left -> right 101 -> 1, 转换为 0 ~ 100
          // elevation : top -> bottom 101 -> 1, 转换为 0 ~ 100
          if (angleValue < 0 || angleValue > 101 || elevationValue < 0 || elevationValue > 101) {
            Service.smarthome.reportLog(Device.model, "illegal angle or elevation value:" + angleValue + " " + elevationValue);
            return;
          }
          if (this.isSendingRdtCmd) {// c01全景绘制后，获取电机方向改到这里了
            this.isSendingRdtCmd = false;
            this.setState({ angle: angleValue, elevation: elevationValue });
            if (this.showPanoAfterReceivedRotateAngle) {
              this.setState({ panoViewStatus: 3 });
              this.showPanoAfterReceivedRotateAngle = false;
              if (this.showPanoToastAfterReceivedRotateAngle) {
                Toast.success("pano_view_success");
                this.showPanoToastAfterReceivedRotateAngle = false;
              }
            }
            // if(this.state.showPanoView){
            //   this.setState({panoViewStatus:3, angle: positionX, elevation: positionY})
            // }
            this.isSendingRdtCmd = false;// end
            return;
          }
          if (Date.now() - this.lastLogTime > 1000) {
            LogUtil.logOnAll("receive ptz direction log: angleValue:" + angleValue + " elevationValue:" + elevationValue);
          }
          this.lastLogTime = Date.now();

          this.setState({ angleViewShowScale: false, angle: angleValue, elevation: elevationValue });
          if (result < 0) {
            if ((result == DirectionViewConstant.CMD_CHECK_END) && !this.state.showPoweroffView) {
              Toast.success("camera_celibrating");
            } else if (result <= -1 && result >= -4 && !this.state.showPoweroffView) {
              Toast.fail("camera_direction_end");
            }
          } else {
          }
        }

      } catch (exception) {
        console.log(`parse angle data error: ${ exception }`);
      }

    } else if (command == MISSCommand.MISS_CMD_CRUISE_STATE_RESP) {
      LogUtil.logOnAll("received cruise state resp", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.value == 1) {
        this.setState({ isCruising: true });
      } else {
        this.setState({ isCruising: false });
      }
    } else if (command == MISSCommand.MISS_CMD_CALL_STATUS_RESP) {
      LogUtil.logOnAll("received CALL_STATUS_RESP", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.type == "hang_up") {
        this.isDeviceHangup = true;
        Toast.success("device_hangup");
        // 表示设备端挂断了通话
        if (this.state.fullScreen) {
          this.restoreOri(5);
        }
        this._stopAll();
        this._doGoBack();
      }

      // if (data.type == "hang_up") {
      //   LogUtil.logOnAll("received CALL_STATUS_RESP do stopCall");
      //   this._stopCall();
      // } else {
      //   LogUtil.logOnAll("received CALL_STATUS_RESP type=", data.type);
      // }
    } else if (command == MISSCommand_ECO.MISS_CMD_NETWORK_STATUS) {
      let ba = base64js.toByteArray(data);
      LogUtil.logOnAll(`receive MISS_CMD_NETWORK_STATUS:${ command } data:${ ba }`);
      if (ba[0] === 0) {
        this._getTargetPush(false, true);
      }
    } else {
      console.log(`receive other command:${ command } data:${ JSON.stringify(data) }`);
    }
  };

  callErrorCheckForBack() {

    // if ((this.callType == 0 || this.callType == 2) && this.callErrorSigal < 2) {
    //   // 音视频通话 指令发出为正常收到回复
    //   return;
    // }
    //
    // if ((this.callType == 0 || this.callType == 2) && !(this.videoCMDError && this.audioCMDError)) {
    //   this.isStartCallError = false;
    //   this._stopAll();
    //   this._doGoBack();
    //   return;
    // }
    // 收到失败回复后，延迟一会再执行获取通话状态
    this.delayToGetCallStatus && clearTimeout(this.delayToGetCallStatus);
    this.delayToGetCallStatus = setTimeout(() => {
      this.isDeviceHangup = true;
      AlarmUtilV2.getOneKeyCallStatus().then((res) => {
        console.log("=============callErrorCheckForBack===============",res);
        if(res[0].code == 0 && res[0].value == 2) {
          this.isStartCallError = true;
          Toast.fail("call_other_side_busy");
          this._stopAll();
          this._doGoBack();
        } else {
          this.isStartCallError = false;
          Toast.fail("speak_failed");
          this._stopAll();
          this._doGoBack();
        }
      }).catch(error => {
        this.isStartCallError = false;
        Toast.fail("speak_failed");
        this._stopAll();
        this._doGoBack();
      });
    },1000);

  }

  _networkChangeHandler = (networkState) => {
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    if (this.isFirstEnter) { // 放到后台的包  刚进来的时候
      return;
    }
    if (this.currentNetworkState == networkState) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;// 已经休眠了，不需要走下面的逻辑
    }
    this.connRetry = 0;//避免重连。
    Service.smarthome.reportLog(Device.model, "处理网络变化" + networkState);
    this.currentNetworkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
      // this.showNetworkDisconnectTimeout = setTimeout(() => {
      //   this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。
      // }, 1300);

      this._handleNetworkError();
      return;
    }
    // 重连重置这个标志位
    this.callIsStop = false;
    this.isErrorToConnect = false;
    this.isNetworkToReconnect = true;

    this._stopAll(false, false);
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      // 网络变更，结束30s的定时操作
      this._clearTimer(this.connectTimeout);
      this._clearTimer(this.reconnectTimeout);
      this.reconnectTimeout = setTimeout(() => {
        if (this.cameraGLView == null) {
          return;
        }
        // this.mNetworkChangedConnection = true;
        Service.smarthome.reportLog(Device.model, `on network changed:${ networkState }`);
        console.log("networkchangehandler");
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  };

  _handleNetworkError() {
    this.isPhoneNetworkError = true;
    this.isCallStart = false;
    this.connectTimeout && clearTimeout(this.connectTimeout);
    this.connectTimeout = setTimeout(() => {
      CameraPlayer.getInstance().videoCallState = 0;
      // 挂断通话 结束当前页面
      if (this.props.navigation.getParam("ori") !== "LANDSCAPE") {
        this.toPortrait(5);
      }
      Toast.success('one_key_call_talkend');
      this._stopAll();
      this._doGoBack();
    },30000);
    console.log("=========================showLoadingView error");
    this.setState({
      showPlayToolBar: false,
      isRecording: false,
      showLoadingView: true,
      showPauseView: false,
      showPoweroffView: false,
      showErrorView: false
    });
  }
  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this._correctOrientation();
      this.toPortrait(4);
      if (Platform.OS === 'android' && this.props.navigation.getParam("ori") === "LANDSCAPE" && !this.isLandscapeToPortraitAndroid) {
        this.isLandscapeToPortraitAndroid = true;
        // 主要针对首页直播横屏进入，设备再切竖屏，直播画面不放大,倍速展示不正确
        setTimeout(() => {
          this.setState({ savedVideoScale: DEFAULT_SCALE_VALUE });
        }, 50);
      }

      if (Platform.OS === 'ios' && !this.isLandscapeToPortrait) {
        // 主要针对首页直播横屏进入，设备再切竖屏，直播画面不放大的问题
        this.isLandscapeToPortrait = true;
        setTimeout(() => {
          this.setState({ savedVideoScale: DEFAULT_SCALE_VALUE });
        }, 50);
      }
      this.setState({
        isWhiteVideoBackground: true,
        minX: kWindowWidth - GESTURE_X,
        minY: TOP_BAR_HEIGHT + 60
      });
      return true;
    }
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return true;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return true;
    }
    this._onPause();
    return false;
  };


  _fetchVipStatus() {
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        CameraConfig.fetchCloudBabyCryStatus(Device.model, vip); // 26c02 需要根据是否是vip做这个事情
        this.isVip = vip;
        this.mVipStatus = status;
        CameraConfig.isVip = vip;
        this.inWindow = inWindow;
        let shouldShowBuyTip = false;
        this.freeHomeSurExpireTime = data.freeHomeSurExpireTime;// 免费看家结束时间
        this.freeHomSurStatus = data.freeHomSurStatus;// 免费看家弹窗状态

        if (this.isVip) {
          let curTime = new Date().getTime();
          let willEndDays = dayjs(endTime).diff(dayjs(curTime), 'day') + 1; // +1 和云存管理页面保持一致
          if (Device.isOwner && (willEndDays == 1 || willEndDays == 3 || willEndDays == 7)) {
            this.cloudVipEndTime = endTime;
            this.cloudVipWillEndDays = willEndDays;
            shouldShowBuyTip = true;
          }
        } else {
          if (Device.isOwner && status == 1 && this.inWindow) {
            shouldShowBuyTip = true;
          }
        }

        if (shouldShowBuyTip) {
          if (!this.mFirmwareSupportCloud || !CameraConfig.isSupportCloud()) {
            return;
          }

          StorageKeys.HIDE_CLOUD_BUY_TIP.then((res) => {
            if (!res) {
              this.setState({ showCloudVipBuyTip: true });
            }
          }).catch(() => {
          });
        } else {
          StorageKeys.HIDE_CLOUD_BUY_TIP = false;
          this.setState({ showCloudVipBuyTip: false });

        }
      })
      .catch((err) => {
        console.log(err);
        // this.isVip = false;
        // this.inWindow = false;
        // StorageKeys.IN_CLOSE_WINDOW = false;
      });
  }

  _getTargetPush(showFormat = false, weakNetWork = false) {
    const { sdcardFullDialog, sdcardSmallDialog, sdcardFormatDialog } = this.state;
    if (typeof (this.isPowerOn) === 'boolean' && !this.isPowerOn) { // 休眠时不再提示
      return false;
    }
    if (this.state.showTargetPushView) {
      LogUtil.logOnAll("_getTargetPush", " is showed target pushview");
      return;
    }
    if (weakNetWork && this.state.resolution > 1) {
      this.targetpushItem = "7";
      this.setState({ showTargetPushView: true }, () => {
        setTimeout(() => {
          this.setState({ showTargetPushView: false });
        }, 15000);
      });
      return;
    }
    if (sdcardFullDialog || sdcardSmallDialog || sdcardFormatDialog) {
      return;
    }
    if (this.language != "zh" || this.state.isInternationServer) {
      return;
    }
    LogUtil.logOnAll(TAG, "china mainland && cn lang, begin to show targetpush, is allow in model:" + CameraConfig.isNewChuangmi(Device.model));
    if (!Device.isOwner) { //chuangmi-12220
      return;
    }
    StorageKeys.IS_TARGET_PUSH_SHOWN.then((result) => {
      LogUtil.logOnAll(TAG, "target push has been showned on this device?" + result);
      if ((typeof (result) != "boolean" || result == false) && !this.state.isNewDevice) {
        this._getTargetPushMessage();
      }
    })
      .catch((err) => {
        console.log("get IS_TARGET_PUSH_SHOWN error: ", err);
      });
    // 展示优先级：云存用户不同状态定向通知>uid+did定向推送>异常状态引导推送（存储卡异常>WDR>夜视设置）
  }

  _getSecurityCodePush() {
    if (this.state.isInternationServer && Device.isOwner) {
      StorageKeys.IS_SECURITY_CODE_TIP_SHOWN.then((result) => {
        LogUtil.logOnAll(TAG, "refresh security code push: isinternationalserver:" + this.isInternationServer + " isOwer:" + Device.isOwner + " cached state:" + result);
        if ((result == null || result == "" || result == false)) {
          this.targetpushItem = "5";
          this.setState({
            isNewDevice: true,
            showTargetPushView: true
          });
          // this.setTargetPushViewShown();
        }
      })
        .catch((err) => {
          console.log("get IS_TARGET_PUSH_SHOWN error: ", err);
        });
    }
  }

  async sdCardabnormal() {
    let showSdCard = await this._getsdCardStoragePushMessage();
    LogUtil.logOnAll(TAG, "targetPush to show sdcard?" + showSdCard);
    if (showSdCard) {
      this.setState({
        showTargetPushView: true
      });
      return;
    }
  }


  async _getTargetPushMessage() {
    let showSdCard = await this._getsdCardStoragePushMessage();
    LogUtil.logOnAll(TAG, "targetPush to show sdcard?" + showSdCard);
    if (showSdCard) {
      // this.setState({
      //   showTargetPushView: true
      // });
      // this.setTargetPushViewShown();
      return;
    }
    LogUtil.logOnAll(TAG, "targetPush isOverExposed?" + this.state.isOverExposed);
    if (this.state.isOverExposed && CameraConfig.overexposureTip()) {
      let showWDR = await this._getWDRModePushMessage();
      LogUtil.logOnAll(TAG, "target push showWDR:" + showWDR);
      if (!showWDR) {
        let showNightMode = await this._getNightModePushMessage();
        LogUtil.logOnAll(TAG, "target push showNightMode:" + showNightMode);
        this.setState({
          showTargetPushView: showNightMode
        });
        if (showNightMode) {
          // this.setTargetPushViewShown();
        }
      } else {
        this.setState({
          showTargetPushView: true
        });
        // this.setTargetPushViewShown();
      }
    } else {
      let showNightMode = await this._getNightModePushMessage();
      LogUtil.logOnAll(TAG, "target push showNightMode:" + showNightMode);
      this.setState({
        showTargetPushView: showNightMode
      });
      if (showNightMode) {
        // this.setTargetPushViewShown();
      }
    }
  }

  async _getsdCardFormatPushMessage() {
    // 1. 存储卡异常（ 0 正常 1 不存在存储卡 2 空间不足 3 异常 4 正在格式化 5 弹出）
    return new Promise((resolve, reject) => {
      CameraPlayer.getInstance().getSdcardStatus()
        .then(({ sdcardCode }) => {
          this.targetpushItem = "6";
          resolve((sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE));
        })
        .catch(({ sdcardCode, error }) => {
          if (typeof (sdcardCode) === 'number' && sdcardCode >= 0) {
            resolve((sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE));
          }
        });
    });
  }

  async _getsdCardStoragePushMessage() {
    // 1. 存储卡异常（ 0 正常 1 不存在存储卡 2 空间不足 3 异常 4 正在格式化 5 弹出）
    return new Promise((resolve, reject) => {
      CameraPlayer.getInstance().getSdcardStatus()
        .then(({ sdcardCode }) => {
          this.targetpushItem = `${ sdcardCode }_sd`;
          resolve((sdcardCode == 3 || sdcardCode == 2
            || sdcardCode == CameraPlayer.SD_CARD_TOO_SMALL_CODE || sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE
            || sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE || sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE));
        })
        .catch(({ sdcardCode, error }) => {
          if (typeof (sdcardCode) === 'number' && sdcardCode >= 0) {
            resolve((sdcardCode == 3));
          }
        });
    });
  }

  async _getWDRModePushMessage() {
    // 1.画面过曝问题且宽动态范围模式（WDR）设置为打开
    return new Promise((resolve, reject) => {
      if (VersionUtil.isUsingSpec(Device.model)) {
        let param = [{ did: Device.deviceID, siid: 2, piid: 5 }];
        Service.spec.getPropertiesValue(param, 2)
          .then((result) => {
            console.log(result);
            if (result instanceof Array && result.length >= 1) {
              this.targetpushItem = "3";
              let htValue = result[0].value;
              resolve(htValue);
            }
          })
          .catch((error) => {
            console.log(error);
            reject(false);
          });
      } else {
        RPC.callMethod("get_prop", [
          'wdr'
        ]).then((res) => {
          this.targetpushItem = "3";
          resolve((res.result[0] == "on"));
        }).catch((err) => {
          console.log(err);
          reject(false);
        });
      }
    });
  }

  async _getNightModePushMessage() {
    // 2. 夜视功能
    return new Promise((resolve, reject) => {

      if (VersionUtil.isUsingSpec(Device.model)) {
        Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[0]], 2)
          .then((res) => {
            console.log(res);
            let code = res[0].code;
            if (code == 0) {
              let value = res[0].value;
              let shouldShowNight = value != 2;
              if (shouldShowNight) {
                if (value == 0) { // spec打开了
                  this.targetpushItem = "2";
                } else if (value == 1) { // spec 关闭了
                  this.targetpushItem = "1";
                }
              }
              resolve(shouldShowNight);
            } else {
              reject(false);
            }
          })
          .catch((err) => {
            reject(false);
          });
      } else {
        RPC.callMethod("get_prop", [
          'night_mode'
        ]).then((res) => {
          let value = res.result[0];
          let showNight = res.result[0] != "0";
          if (value == "2") {
            this.targetpushItem = "2"; // 开关打开，提示画面黑白
          } else if (value == "1") {
            this.targetpushItem = "1"; // 开关关闭，提示画面太黑
          }
          this.targetpushItem = res.result[0];
          resolve((showNight));
        }).catch((err) => {
          reject(false);
        });
      }

    });
  }

  _getOperationBanner() {
    if (CameraConfig.shouldDisplayBannerTips(Device.model)) {
      StorageKeys.OPERATION_CLICKED_KEY.then((result) => {
        this.setState({ clickedBannerShortKey: result });
      })
        .catch((err) => {
          console.log(err);
        });
      this._getOperationBannerOnline();

    }

  }

  _getOperationBannerOnline() {
    let platformId = 1;
    if (Platform.OS === "android") {
      platformId = 2;
    }
    // let bannerUrl = `https://home.mi.com/cgi-op/api/v1/recommendation/banner?type=25&platform=${platformId}&uid=${uid}&appTabType=1`
    let bannerUrl = `https://home.mi.com/cgi-op/api/v1/recommendation/banner?type=25&platform=${ platformId }&appTabType=1`;
    // let bannerUrl = `http://st.iot.home.mi.com/cgi-op/api/v1/recommendation/banner?type=25&platform=${platformId}&appTabType=1`;

    fetch(bannerUrl, { headers: { 'user-agent': Platform.OS === "android" ? "android" : "iphone" } })
      .then((response) => response.text())
      .then((responseText) => {
        // console.log("banner tips:", responseText);
        let res = JSON.parse(responseText);
        let targetItem = null;
        if (res && res.code == 0 && res.data && res.data.list) {
          for (let i = 0; i < res.data.list.length; ++i) {
            let item = res.data.list[i];
            let curTimeStamp = new Date().getTime(); // 整型，用于做唯一性标识
            if (curTimeStamp >= item.beginTime && curTimeStamp < item.endTime) {
              targetItem = item;
              break;
            }
          }

          if (targetItem) {
            if (!this.bannerItem || targetItem.shortKey != this.bannerItem.shortKey) {
              this.bannerItem = targetItem;
              TrackUtil.reportClickEvent("Camera_Recommend_Show");// 推荐运营位曝光
              this.setState({ bannerShortKey: targetItem.shortKey });
              // let bannerItemStr = JSON.stringify(item);
              // StorageKeys.OPERATION_BANNER_ITEM = bannerItemStr;
            }
          } else {
            this.bannerItem = null;
            // StorageKeys.OPERATION_BANNER_ITEM = "";
            this.setState({ bannerShortKey: "0" });
          }
        }
      })
      .catch((err) => {
        console.log(`_getOperationInfo error: ${ err }`);
      });
  }

  // query whether is power off;
  queryPowerOffProperty() {
    // todo fist check power on/off
    TrackConnectionHelper.onPowerBeginChecked();
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[6]])
        .then((result) => {
          let isOk = result[0].code == 0;
          if (isOk) {
            TrackConnectionHelper.onPowerEndChecked();
            this.isFirstEnter = false;
            let isPowerOn = result[0].value;
            this.isPowerOn = isPowerOn;

            this._powerOffHandler(isPowerOn, false, false);//powerOff notify ui only
          } else {
            this._powerOffHandler(true, false, false);
          }
        })
        .catch((error) => {
          TrackConnectionHelper.onPowerEndChecked();
          this.isFirstEnter = false;
          StorageKeys.IS_POWER_ON
            .then((res) => {
              if (typeof (res) == "string" || res === null) {
                res = true;// 没有设置过，默认当做是非休眠状态吧。
              }
              this._powerOffHandler(res, false, false);
            })
            .catch((err) => { // 查询本地属性都失败了。
              this._powerOffHandler(true, false, false);
            });

        });
    } else {
      RPC.callMethod("get_prop", ["power"])
        .then((res) => {

          TrackConnectionHelper.onPowerEndChecked();
          this.isFirstEnter = false;
          console.log(res);
          // this.setState({ isSleep: false, showPlayToolBar: true })
          let isPowerOn = res.result[0] === "on";
          this.isPowerOn = isPowerOn;
          this._powerOffHandler(this.isPowerOn, false, false);
        })
        .catch((err) => { // 查询状态出错  不管  直接去查询
          TrackConnectionHelper.onPowerEndChecked();
          this.isFirstEnter = false;
          Service.smarthome.reportLog(Device.model, "onPowerOn");
          StorageKeys.IS_POWER_ON
            .then((res) => {
              if (typeof (res) === "string" || res == null) {
                res = true;// 没有设置过，默认当做是非休眠状态吧。
              }
              this._powerOffHandler(res, false, false);
            })
            .catch((err) => { // 查询本地属性都失败了。
              this._powerOffHandler(true, false, false);
            });
        });
    }

  }

  startCheckLog() {
    fetchLogUploaderStatus()
      .then((result) => {
        let { showDialog, msg } = result;
        Service.smarthome.reportLog(Device.model, `fetchLogUploaderStatus: ${ result.showDialog }`);
        clearInterval(this.logInterval);
        if (showDialog) {
          this.setState({ showLogDialog: true, logDialogContent: msg });
        }
      })
      .catch((err) => {
        Service.smarthome.reportLog(Device.model, `fetchLogUploaderStatus2: ${ JSON.stringify(err) }`);
        console.log(JSON.stringify(err));
      });
  }

  _startConnect() {

    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      console.log("=========is in background");
      this.setState({ showLoadingView: false });
      return;
    }
    console.log("=========================",CameraPlayer.getInstance().isConnected())
    if (!this.state.showLoadingView && !CameraPlayer.getInstance().isConnected()) { // 如果没有loading
      console.log("=========================showLoadingView _startConnect");

      this.setState({ showLoadingView: true, showErrorView: false, showPlayToolBar: false });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      Service.smarthome.reportLog(Device.model, "already connected, start send video start");
      TrackConnectionHelper.onConnected();
      this.setState({ pstate: MISSConnectState.MISS_Connection_Connected });
      this._realStartVideo();
      return;
    }
    Service.smarthome.reportLog(Device.model, "not connected, try start connect");
    this.setState({ pstate: 0, error: 1 });
    TrackConnectionHelper.startConnect();
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
    if (!Device.isOnline) {
      this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_offline'] });
      OfflineHelper.getLastOnlineTime()
        .then((result) => {
          this.setState({ lastOfflineTime: `${ LocalizedStrings['offline_time_str'] }: ${ result }` });
        })
        .catch((rr) => {
        });
    }
  }

  _realStartVideo() {
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    Service.smarthome.reportLog(Device.model, "send videoStart cmd");
    this.cameraGLView && this.cameraGLView.stopRender();
    this.cameraGLView && this.cameraGLView.startRender();// startBindVideo
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
      .then((retCode) => {
        console.log("startVideo success ", retCode, "mute", this.state.isMute, this.state.pstate);
        if (this.cameraGLView == null || this.destroyed) {
          return;
        }
        if (this.state.pstate == 2 || this.state.pstate == 3) {
          this.isFirstErrorEnter = true;

          this.connectTimeout && clearTimeout(this.connectTimeout);
          this.setState({ showLoadingView: false });// 已经渲染过  直接跳过去
          // 通话中，实时流再次连接，需要请求下通话状态
          if (this.state.isCalling && (this.isErrorToConnect || this.isPhoneNetworkError)) {
            LogUtil.logOnAll(TAG,"is in call");
            AlarmUtilV2.getOneKeyCallStatus().then((res) => {
              if(res[0].code == 0 && res[0].value != 2) {
                Toast.success("user_hangup");
                // 已经处于非通话状态
                if (this.props.navigation.getParam("ori") !== "LANDSCAPE") {
                  this.toPortrait(5);
                }
                this._stopAll();
                this._doGoBack();
              }
            }).catch(error => {

            });
            this.isErrorToConnect = false;
            this.isPhoneNetworkError = false;
          }
        }
        if (!this.state.isMute || this.state.isRecording) {
          // need renew AudioQueueRef for sound play
          if (!this.state.isRecording) {
            this.cameraGLView.stopAudioPlay();
          }

          Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
            console.log("resume audioplay ", retCode);
          });
          if (!this.state.isRecording) {
            this.cameraGLView.startAudioPlay();
          }
        }
        // 每次video-start都重新发送一次quality change吧
        if (this.videoQualityFetched || this.state.isRecording) {
          this.sendResolutionCmd(this.state.isRecording ? 3 : this.state.resolution, this.state.isRecording ? true : false);// 每次收到firstFrame都需要刷新resolution，避免去往其他页面再回来，分辨率变低了却不知道
        }
        // 走到这里
        LogUtil.logOnAll("_startCall for _realStartVideo fromOneKeyCall=", this.fromOneKeyCall,this.isCallStart);
        if (!this.isCallStart) {
          this.isCallStart = true;
          this._startCall(true);
        }

        if (this.startCallFlag && this.fromOneKeyCall) {
          this.fromOneKeyCall = false;
          this.startCallFlag = false;
        }
      })
      .catch((err) => {
        this.cameraGLView && this.cameraGLView.stopRender();
        if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
          CameraPlayer.getInstance().resetConnectionState();
          Service.smarthome.reportLog(Device.model, "video-start的时候出错了:" + err);
          console.log("video-start error");
          this.queryNetworkJob();
          return;
        }

        this.setState({
          pstate: 0,
          showLoadingView: false,
          showErrorView: true,
          errTextString: `${ LocalizedStrings["camera_connect_error"] } ${ err } ${ LocalizedStrings["camera_connect_retry"] }`
        });// 已经渲染过  直接跳过去
      });

  }

  showLoadingView() {
    console.log("=========================showLoadingView--------");
    this.setState({ showLoadingView: true });
  }

  _onVideoClick() {
    LogUtil.logOnAll(TAG, "live page onVideoClick");
    if (!CameraPlayer.getInstance().isConnected()) {
      return;
    }
    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar
      };
    }, () => {
      this._hidePlayToolBarLater();
    });
    // this.setState({
    //   showPlayToolBar: !this.state.showPlayToolBar
    // });

    // if (this.state.showPlayToolBar) { // setState在下个loop中才会生效，所以这里还要用老的状态检查
    //   if (!this.state.fullScreen) {
    //     // 横屏时不定时隐藏toolbar
    //     this._hidePlayToolBarLater();
    //   }
    //   if (this.showPlayToolBarTimer) {
    //     clearTimeout(this.showPlayToolBarTimer);
    //     this.showPlayToolBarTimer = null;
    //   }
    // }
    console.log("click video view");
  }

  // 设置定时器
  videoScaleTimer = null;

  // 倍数改变时的函数
  _onVideoScaleChanged(params) {
    console.log("++++++++=====+++++++++",params.nativeEvent?.scale);

    let scale = params.nativeEvent?.scale;
    // 当返回有倍数时 清除定时器 并更新倍数 相当于防抖操作 一直触发事件就一直清空定时器
    if (scale) {
      clearTimeout(this.videoScaleTimer);

      this.videoScaleTimer = setTimeout(() => {
        console.log("tick" + scale);
        this._updateScale(scale); // 更新倍数
      }, 0);
    }

    this._onReceiveVideoRenderedEvent(params);
    // 进行节流操作 
    let endTime = Date.now();
    if ((endTime - this.startScaleTime) < 50) {
      console.log('_onVideoScaleChanged', scale);
      return;
    }
    this.startScaleTime = endTime;

    this._updateScale(scale);
  }

  _updateScale(scale) {
    if (scale) {
      scale = Number(scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {// 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
      if (!this.state.fullScreen) {
        this.videoPortraitScale = scale;// 保存竖屏下的videoScale
      }
      this.angleView?.setScale(scale);
      if (!this.state.showCameraAngleView) {
        this.setState(() => {
          return { showCameraAngleView: true, angleViewShowScale: true };
        }, () => {
          this.angleView?.setScale(scale);
          if (scale > 1 && this.state.showPlayToolBar) {
            // this.setState({ showPlayToolBar: false });
          } else if (scale == 1 && !this.state.showPlayToolBar) {
            this.setState({ showPlayToolBar: true });
          }
        });
      }
      this.setState({
        videoScale: scale,
        showCameraAngleView: true,
        angleViewShowScale: true,
        // showPlayToolBar: scale > 1 ? false : true
      });
      if ((Date.now() - this.startScaleReportTime) > 1000) {
        this.startScaleReportTime = Date.now();
        if (scale == 1) {
          TrackUtil.reportClickEvent('camera_ZoomOutFull_Num');
        } else if (scale > this.tmpScale) {
          TrackUtil.reportClickEvent('camera_ZoomIn_Num');
        } else {
          TrackUtil.reportClickEvent('camera_ZoomOut_Num');
        }
      } else {
        this.tmpScale = scale;
      }
    }
  }

  _onReceiveVideoRenderedEvent(params) {
    if (params && params.nativeEvent && params.nativeEvent.firstVideoFrame && Host.isAndroid) {
      console.log(TAG, "received firstVideoFrame");
      this.isFirstFrameReceived = true;
      this.setState({ showDefaultBgView: false, showLoadingView: false, whiteTitleBg: false, showPlayToolBar: true });
      clearTimeout(this.snapshotTimer);
      setTimeout(() => {
        AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip);
      }, 100);
      // important !!!!渲染了第一帧数据，开始缩放, 避免没有收到的时候，就发送这个通知，导致UI出问题了。
      if (!Host.isAndroid) {// 只有android有这个通知。。。。
        return;
      }
      StorageKeys.VIDEO_SCALE.then((result) => {
        this.setVideoScale();
      }).catch((err) => {
        this.setVideoScale();
      });
      TrackConnectionHelper.onFrameRendered();
      TrackConnectionHelper.report();
    }
  }

  _onVideoPTZDirectionCtr(params) {
    if (!this.isPtz) {
      return;
    }

    console.log("received PTZ direction control from CameraRenderView");
    if (!this.enablePtzRotation) {
      return;
    }

    if (params && params.nativeEvent && params.nativeEvent.direction) {
      let direction = Number(params.nativeEvent.direction);
      if (this.isHorizontalPTZ && (direction == DirectionViewConstant.DIRECTION_UP || direction == DirectionViewConstant.DIRECTION_BOTTOM)) {
        return;
      }

      if (this.isReadonlyShared) {
        Toast.fail('cloud_share_hint');
        return;
      }
      LogUtil.logOnAll("screen drag causes ptz direction cmd");
      this._sendDirectionCmd(direction);
      this._sendDirectionCmd(DirectionViewConstant.CMD_OFF);
    }
  }

  canStepOut(ignoreSleep = false) {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return false;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return false;
    }
    if (this.state.isSleep && !ignoreSleep) {
      Toast.success(this.getPowerOffString());
      return false;
    }
    return true;
  }

  _handleSdcardClick(skipErrorCode = false) {

    TrackUtil.reportClickEvent("Camera_Playback_ClickNum"); // Camera_Playback_ClickNum

    this._handleSdcardPtzClick();
  }

  _handleSdcardV1V3Click(skipErrorCode = false) {
    // if(this.isHandlingSdcard) {//处理上一次点击后，又立马点击 防止重复。
    //   return;
    // }
    TrackUtil.reportClickEvent("Camera_Playback_ClickNum"); // Camera_Playback_ClickNum
    if (!this.canStepOut()) {
      return;
    }

    if (this.state.pstate < 2) {
      // 未连接
      Toast.success("no_playback_for_connect");
      return;
    }
    if (this.sdcardCode == -1) { // 这里应该弹sd卡状态获取失败。。。
      Toast.success("sdcard_page_desc_failed");
      return;
    }

    if (this.sdcardCode == 4) {
      Toast.success("formating_error");
      return;
    }
    if (this.sdcardCode == 3) { // sdcard error
      this.props.navigation.navigate("SDCardSetting");
      return;
    }

    if (this.sdcardCode == 1 || this.sdcardCode == 5) { // nosdcard
      // todo navigate to no memory card page
      this.props.navigation.navigate("SDCardSetting");// 这个页面 如果没有sd卡 也会自己显示空页面。
      return;
    }
    this.props.navigation.navigate("SdcardTimelinePlayerPage", { title: "sdcardPage" });// todo 待实现回看的代码
  }


  _handleSdcardPtzClick() {
    if (!this.canStepOut(true)) {
      return;
    }

    if (this.sdcardCode == 4) {
      Toast.success("formating_error");
      return;
    }

    // if (VersionUtil.Model_Chuangmi_022 == Device.model) {
    //   let data = { sdcardGetSuccess: true, sdcardStatus: sdcardCode, isVip: this.isVip };
    //   Service.miotcamera.showPlaybackVideos(JSON.stringify(data));
    // } else {
    let showSdcard = this.showSdcardPage;
    if (this.sdcardCode == 3 || this.isVip && (this.sdcardCode == 1 || this.sdcardCode == 4)) { // 卡异常，无卡，卡正在格式化，都跳cloud页面
      showSdcard = false;
    }
    let param = {
      sdcardCode: this.sdcardCode,
      isVip: this.isVip,
      isShowSdcardPage: showSdcard,
      isSupportCloud: this.mFirmwareSupportCloud && CameraConfig.isSupportCloud()
    };
    LogUtil.logOnAll("sdcard cloud timeline page: param==", JSON.stringify(param));
    // 进入回看前 清空一次SdFileManager里的列表。
    SdcardEventLoader.getInstance().clearSdcardFileList();
    console.log(param, 9999999999);
    this.props.navigation.navigate("SdcardCloudTimelinePage", param);// todo 待实现回看的代码
    // }

  }

  getPowerOffString() {
    return this.isSupportPhysicalCover ? "camera_physical_covered" : "camera_power_off";
  }

  render() {
    return (
      <View style={ styles.main }>
        {/* <SafeAreaView style={{ backgroundColor: "#ffffff" }}></SafeAreaView> */ }

        { this._renderVideoLayout() }
        {/*{ this._renderCloudVipRenewView() }*/}
        { this._renderControlLayout() }
        { this._renderGlobalLoading() }
        { this._renderTimeoutDialog() }

        { this._renderSleepNotifyDialog() }


        <DeviceOfflineDialog
          ref="powerOfflineDialog"
        />
        <NoNetworkDialog
          ref="noNetworkDialog"
        />
        { this._renderLogUploaderDialog() }
        { this._renderPermissionDialog() }
        { this._renderVisitInfoDialog() }
        {/* <SafeAreaView></SafeAreaView> */ }
        { this._renderGBFDialog() }
        {/* {this._renderOneKeyCallDialog()} */ }
        { this._renderSDCardFormatDialog() }
        { this._renderSDCardFullDialog() }
        { this._renderSDCardSmallDialog() }
        {/*<View style={{ backgroundColor: '#00ff00', position: 'absolute', top: 0,bottom:0, right: 10, width: 100, height: kScreenHeight }}>*/ }
        {/*<View style={{ backgroundColor: '#00ff00', position: 'absolute', top: 0,bottom:0,zIndex:99, right: 10, width: 100, height: this.winPortraitHeight }}>*/ }

        {/*</View>*/ }
        {this._renderVideoCallView()}
        {this.callVoiceDialog()}
      </View>
    );
  }

  callVoiceDialog() {
    this.options = Array.from({ length: 10 }, (v, i) =>  (i + 1) * 10);
    let subtitle = `${this.state.tempCallVolumeValue}%`;
    let defaultValueIndex = this.options.indexOf(this.state.callVolumeValue);
    // height: kWindowWidth * 0.7,
    let modalStyle = this.state.fullScreen ? {width: kScreenHeight * 0.5, alignSelf: 'center', bottom: kWindowWidth * 0.15, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {}

    return (
      <AbstractDialog
        visible={this.state.showChangeVolumeDialog}
        title={LocalizedStrings['camera_volume_setting']}
        subtitle={subtitle}
        style={modalStyle}
        showSubtitle={true}
        useNewTheme={true}
        onDismiss={(_) => this.setState({ showChangeVolumeDialog: false })}
        buttons={[{
          text: LocalizedStrings['fold'],
          colorType:'grayLayerBlack',
          callback: () => {
            this.setState({ showChangeVolumeDialog: false });
          }
        }, 
        // {
        //   text: I18n.ok,
        //   callback: (value) => {
        //     console.log("result1", this.state.lightValue,this.state.tempLightValue);
        //     this.setState({ showChangeVolumeDialog: false });
        //     //对讲音量设置
        //     let params = [{sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME, value: this.volumeNewValue}];
        //     AlarmUtilV2.setSpecPValue(params).then((res)=>{
        //       console.log("=====+=+=+=+=+=+=+=+=+=+=+==",res,this.volumeNewValue);
        //       this.setState({ callVolumeValue: this.volumeNewValue });
        //     }).catch((err)=>{
        //       console.log("setSpeakerVolume err=", JSON.stringify(err));
        //     });
        //   }
        // }
      ]}
      >

        <View style={{flex: 1}}>
          {/*<SlideGear*/}
          {/*  options={this.options}*/}
          {/*  value={defaultValueIndex}*/}
          {/*  leftTextColor="#32BAC0"*/}
          {/*  containerStyle={{ marginHorizontal: 28, height: 30 }}*/}
          {/*  onValueChange={index => {*/}
          {/*    console.log("===onValueChange===",index)*/}
          {/*  }}*/}
          {/*  onSlidingComplete={index => {*/}
          {/*    console.log("===onSlidingComplete===",index,this.state.callVolumeValue,this.options[index])*/}
          {/*    this.volumeNewValue = this.options[index];*/}
          {/*    this.setState({ tempCallVolumeValue: this.options[index] });*/}
          {/*  }}*/}
          {/*  showEndText={false}*/}
          {/*/>*/}
          <SlideGear
            options={this.options}
            value={defaultValueIndex}
            maximumTrackTintColor={ DarkMode.getColorScheme() == 'dark' ? 'xm#2D2D2D' : "#E2E2E2" }
            minimumTrackTintColor={ "#32BAC0" }
            type={SlideGear.TYPE.RECTANGLE}
            blockStyle={{backgroundColor: '#32BAC0', width: 20}}
            indicatorTextStyle={ { color: DarkMode.getColorScheme() == 'dark' ? '#0000000F' : 'black' } }
            // leftTextColor="#32BAC0"
            stopAtIndicatorText={ true }

            containerStyle={{ marginHorizontal: 28, height: 30 }}
            onValueChange={index => {
              console.log("===onValueChange===",index)
              this.setState({ tempCallVolumeValue: this.options[index] });
            }}
            onSlidingComplete={index => {
              console.log("===onSlidingComplete===",index,this.state.callVolumeValue,this.options[index])
              this.volumeNewValue = this.options[index];
              this.setState({ tempCallVolumeValue: this.options[index] });
              let params = [{sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME, value: this.volumeNewValue}];
              AlarmUtilV2.setSpecPValue(params).then((res)=>{
                console.log("=====+=+=+=+=+=+=+=+=+=+=+==",res,this.volumeNewValue);
                this.setState({ callVolumeValue: this.volumeNewValue });
              }).catch((err)=>{
                console.log("setSpeakerVolume err=", JSON.stringify(err));
              });
            }}
            showEndText={false}
          />
          <View style={{ flex: 1, flexDirection: 'row',justifyContent: 'space-between', marginBottom: 26, marginTop: 5, marginHorizontal: 28 }}>
            <Text style={{fontSize: 13, color: '#00000066'}}>{"0"}</Text>
            <Text style={{fontSize: 13, color: '#00000066'}}>{"100%"}</Text>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderVideoCallView() {
    let videoTitleHeight = this._getTitleBarPortraitHeight();
    if (!this.state.videoOpen) {
      return null;
    }
    let encodeWidth = this.state.fullScreen ? 480 : 320;
    let encodeHeight = this.state.fullScreen ? 320 : 480;
    // encodeWidth = 320;
    // encodeHeight = 480;
    let callVideoStyle;
    if (this.state.fullScreen) {
      callVideoStyle = {
        zIndex: 99,
        width: 150,
        borderRadius: 10,
        height: 100,
        backgroundColor: "xm#000000"
        // transform: [{ rotate: '90deg' }]
      }
    } else {
      callVideoStyle = {
        zIndex: 99,
        width: 100,
        borderRadius: 10,
        height: 150,
        backgroundColor: "xm#000000"
      }
    }
    return (
      <View
        style={{
          zIndex: 99,
          position: 'absolute',
          flexDirection: 'column',
          marginTop: this.state.minY,
          marginLeft: this.state.minX,
        }}
      >
        {/*justifyContent: 'center',alignItems: 'center'*/}
        <View style={{ width: this.state.fullScreen ? 153 : 103, height: this.state.fullScreen ? 103 : 153, justifyContent: 'center',alignItems: 'center' }} >
        {/*<View>*/}
          <MHCameraCaptureView
            ref={(ref) => {this.cameraCaptureView = ref;}}
            did={Device.deviceID}
            encoderFps={15}
            encoderWidth={encodeWidth}
            encoderHeight={encodeHeight}
            fullScreen={this.state.fullScreen}
            averageBitRate={480*320*8*24}
            cameraType={this.state.cameraType}
            style={callVideoStyle}
            {...this.myPanResponder.panHandlers}
          />

         <View ref={(ref) => { this.tinyBallBorder = ref; }}
              style={{
                zIndex: 100,
                width: this.state.fullScreen ? 153 : 103,
                height: this.state.fullScreen ? 103 : 153,
                backgroundColor: "transparent",
                position: "absolute",
                borderRadius: 5, borderWidth: 2, borderColor: this.state.darkMode ? "#xmdddddd" : "white",
                // top: -1.5, left: -1.5
              }} pointerEvents={"box-none"}/>
        </View>
      </View>);
  }

  _renderSDCardSmallDialog() {
    let myButtons = [
      {
        text: LocalizedStrings["offline_divice_ok"],
        // style: { color: 'lightblue' },
        callback: (_) => {
          this.setState({ sdcardSmallDialog: false });
        }
      }
    ];
    return (
      <MessageDialog
        visible={ this.state.sdcardSmallDialog }
        title={ LocalizedStrings['sdcard_small_title'] }
        message={ LocalizedStrings['sdcard_small_msg'] }
        buttons={ myButtons }
        onDismiss={ () => {
          this.setState({ sdcardSmallDialog: false });
        } }
      />
    );
  }

  _renderOneKeyCallDialog() {
    if (VersionUtil.isAiCameraModel(Device.model)) {
      return (
        <AbstractDialog
          visible={ this.state.showOneKeyCallDialog }
          title={ LocalizedStrings['someone_calling'] }
          useNewTheme
          canDismiss={ false }
          buttons={ [
            {
              text: LocalizedStrings["talk_for_push_reject"],
              // style: { color: 'lightpink' },
              callback: (_) => {
                this.setState({ showOneKeyCallDialog: false });
                AlarmUtilV2.putOneKeyCallStatus(0).then((res) => {
                  LogUtil.logOnAll("putOneKeyCallStatus(0)", JSON.stringify(res));
                }).catch((err) => {
                  LogUtil.logOnAll("putOneKeyCallStatus(0) err", JSON.stringify(err));
                });
              }
            },
            {
              text: LocalizedStrings["talk_for_push_accept"],
              // style: { color: 'lightblue' },
              callback: (_) => {
                if (!CameraPlayer.getInstance().isConnected()) {
                  Toast.fail('talk_for_push_connecting');
                  return;
                }
                LogUtil.logOnAll("_startCall for _renderOneKeyCallDialog fromOneKeyCall=", this.fromOneKeyCall);
                this._startCall();
                // Toast.loading('talk_for_push_calling');
              }
            }
          ] }>
          <View></View>
        </AbstractDialog>
      );
    }
  }

  _showGBFDialog() {
    if (Device.isReadonlyShared) {
      console.log("isReadonlyShared not supported");
      return;
    }
    StorageKeys.IS_FACE_PRIVACY_GBF_NEEDED.then((result) => {
      if (result !== false) {
        StorageKeys.IS_FACE_PRIVACY_GBF_NEEDED = false;
        this.setState({ showGBFDialog: true });
      }
    }).catch((err) => {
      StorageKeys.IS_FACE_PRIVACY_GBF_NEEDED = false;
      this.setState({ showGBFDialog: true });
    });
  }

  _renderGBFDialog() {
    if (VersionUtil.isAiCameraModel(Device.model)) {
      return (
        <MessageDialog
          visible={ this.state.showGBFDialog }
          title={ LocalizedStrings['face_service_tips'] }
          message={ this.isCloudServer ? LocalizedStrings['eu_face_service_tips_message'] : LocalizedStrings['face_service_tips_message'] }
          canDismiss={ false }
          buttons={ [
            {
              text: LocalizedStrings["license_negative_btn_face"],
              // style: { color: 'lightpink' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_CANCEL);
                this.setState({ showGBFDialog: false });
              }
            },
            {
              text: LocalizedStrings["license_positive_btn_face"],
              // style: { color: 'lightblue' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_ACCEPT);
                this.setState({ showGBFDialog: false });
              }
            }
          ] }
        />
      );
    }
  }

  _renderSDCardFullDialog() {
    let myButtons = [
      {
        text: LocalizedStrings["btn_cancel"],
        // style: { color: 'lightpink' },
        callback: (_) => {
          this.setState({ sdcardFullDialog: false });
        }
      },
      {
        text: LocalizedStrings["btn_confirm"],
        // style: { color: 'lightblue' },
        callback: (_) => {
          this.setState({ sdcardFullDialog: false });
          this.props.navigation.navigate("SDCardSetting", { toFormateSDCard: true, needShowFormatDialog: false });
        }
      }
    ];
    if (Device.isReadonlyShared) {
      myButtons = [
        {
          text: LocalizedStrings["offline_divice_ok"],
          // style: { color: 'lightpink' },
          callback: (_) => {
            this.setState({ sdcardFullDialog: false });
          }
        }
      ];
    }

    return (
      <MessageDialog
        visible={ this.state.sdcardFullDialog }
        title={ LocalizedStrings['sdcard_notfull_title'] }
        message={ Device.isReadonlyShared && Device.permitLevel === 36 ? LocalizedStrings['sdcard_notfull_message_readonly'] : LocalizedStrings['sdcard_notfull_message'] }
        // canDismiss={false}
        buttons={ myButtons }
        onDismiss={ () => {
          this.setState({ sdcardFullDialog: false });
        } }
      />
    );
  }

  _renderSDCardFormatDialog() {
    let myButtons = [
      {
        text: LocalizedStrings["btn_cancel"],
        // style: { color: 'lightpink' },
        callback: (_) => {
          this.setState({ sdcardFormatDialog: false });
        }
      },
      {
        text: LocalizedStrings["btn_confirm"],
        // style: { color: 'lightblue' },
        callback: (_) => {
          this.setState({ sdcardFormatDialog: false });
          this.props.navigation.navigate("SDCardSetting", { toFormateSDCard: true, needShowFormatDialog: false });
        }
      }
    ];
    if (Device.isReadonlyShared || this.sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE) {
      myButtons = [
        {
          text: LocalizedStrings["offline_divice_ok"],
          // style: { color: 'lightpink' },
          callback: (_) => {
            this.setState({ sdcardFormatDialog: false });
          }
        }
      ];
    }
    return (
      <MessageDialog
        visible={ this.state.sdcardFormatDialog }
        title={ this.sdcardCode == 3 ? LocalizedStrings["sdcard_status_error"] : LocalizedStrings[`sdcard_format_title_${ this.sdcardCode }`] }
        message={ Device.isReadonlyShared ? LocalizedStrings[`sdcard_format_message_readonly_${ this.sdcardCode }`]
          : LocalizedStrings[`sdcard_format_message_${ this.sdcardCode }`] }
        // canDismiss={false}
        buttons={ myButtons }
        onDismiss={ () => {
          this.setState({ sdcardFormatDialog: false });
        } }

      />
    );
  }

  _renderGlobalLoading() {
    if (!this.state.showGlobalLoading) {
      return null;
    }
    return (
      <View style={ {
        zIndex: 4,
        position: "absolute",
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#00000066"
      } }>
        <LoadingView style={ { width: 100, height: 100 } }/>
      </View>
    );

  }

  _renderTimeoutDialog() {
    return (
      <MessageDialog
        message={ LocalizedStrings["network_fake_connected"] }
        buttons={
          [
            {
              text: LocalizedStrings['action_confirm'],
              callback: () => {
                this.exitPackage();
              }
            }
          ]
        }
        onDismiss={ () => {
          // this.setState({ showTimeoutDialog: false });
        } }
        visible={ this.state.showTimeoutDialog }
      >

      </MessageDialog>
    );
  }

  exitPackage = () => {
    Package.exit();
  };

  _getStatusBarHeight() {
    let statusBarHeight = StatusBarUtil._getInset("top");
    return statusBarHeight;
  }

  _getTitleBarPortraitHeight() {
    let titleBarHeight = navigationBarHeightFat;
    let statusBarHeight = this._getStatusBarHeight();

    titleBarHeight += statusBarHeight;
    return titleBarHeight;
  }

  /**
   * @Author: byh
   * @Date: 2023/11/18
   * @explanation:
   * 计算屏幕宽高比例，动态计算视频区域的高度，底部操作部分的高度暂时固定
   * 视频宽高比 2:3
   *********************************************************/
  shouldResizeVideoHeight() {
    let bottomHeight = 100;
    let needHeight = kWindowWidth * 3 / 2 + bottomHeight;
    return needHeight > kScreenHeight;
  }

  _initHeightValues() {
    // kScreenHeight手机窗口高度
    // winHeight = kScreenHeight - this._getStatusBarHeight()
    console.log("_initHeightValues1", kScreenHeight, kWindowWidth);

    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let kScreenHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);

    console.log("_initHeightValues2", kScreenHeight, kWindowHeight, StatusBarUtil._getInset("top"), StatusBarUtil._getInset("bottom"),kWindowWidth, winWidth, winHeight);

    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    this.winPortraitWidth = width;
    this.winPortraitHeight = height;
    // 视频的高度顶到状态栏去  宽高比 2:3
    // this.videoPortraiHeight = width * 9 / 16;
    //视频底部操作区域高度需要233
    let needVideoHeight = width * 3 / 2;
    // 某些手机不是整数
    let kScreenHeightInt = Math.round(kScreenHeight);
    let kWindowHeightInt = Math.round(kWindowHeight);
    let statusbarHeightInt = Math.round(StatusBarUtil._getInset("top"));
    let isHasBottomBar = false;
    let bottomBarHeight = 0;
    if (Platform.OS == 'android' && kScreenHeightInt - kWindowHeightInt > statusbarHeightInt) {
      isHasBottomBar = true;
      bottomBarHeight = kScreenHeightInt - kWindowHeightInt - statusbarHeightInt;
    }
    // vivo x27 全屏状与导航
    // if ((isHasBottomBar && bottomBarHeight < 30) || Host.systemInfo.mobileModel == 'V1838A') {
    if (Platform.OS == 'android' && (isHasBottomBar && bottomBarHeight < 35)) {
      bottomBarHeight = kScreenHeightInt - kWindowHeightInt;
    }

    let allLeftHeight = Platform.OS == 'ios' ? kScreenHeight - bottomBarHeight - 89 - 119 - 16 : kScreenHeight - bottomBarHeight - 89 - 119;
    console.log("_initHeightValuesoo",allLeftHeight,bottomBarHeight);
    this.opTopMargin = Math.round(allLeftHeight * 0.09);
    this.opMiddleMargin = Math.round(allLeftHeight * 0.05);
    this.videoPortraiHeight = Math.round(allLeftHeight * 0.86);
    DEFAULT_SCALE_VALUE = this.videoPortraiHeight / (9 / 16 * kWindowWidth);

    // let leftHeight = kScreenHeightInt - needVideoHeight;
    // LogUtil.logOnAll("leftHeight:",kScreenHeight,kWindowHeight,leftHeight,isHasBottomBar);
    // console.log("needVideoHeight:",needVideoHeight,kScreenHeight);
    // if (isHasBottomBar) {
    //   leftHeight = leftHeight - bottomBarHeight;
    // }
    // if(leftHeight < 233) {
    //   needVideoHeight = kScreenHeight - 233;
    //   if (isHasBottomBar) {
    //     needVideoHeight = needVideoHeight - bottomBarHeight;
    //   }
    // }
    // this.videoPortraiHeight = needVideoHeight;

    let optionAreaHeight = this.winPortraitHeight - this.videoPortraiHeight - fixControlBarHeight - navigationBarHeightFat - this._getStatusBarHeight();
    console.log("_initHeightValues3", this.videoPortraiHeight,kScreenHeight, kWindowWidth, winWidth, winHeight, this._getStatusBarHeight());
    //操作区域的高度 = 页面window高度-视频区域高度-截图区域高度 - 导航栏高度 - 状态栏区域高度
    let directBottom;
    if (optionAreaHeight >= 300) {// 根据不同机型做适配，区分处理。
      directBottom = 50;
      this.directViewHeight = 220;
      this.directViewTop = 20;
    } else if (optionAreaHeight >= 270) {
      directBottom = 30;
      this.directViewHeight = 180;
      this.directViewTop = 20;
    } else {
      directBottom = 30;
      this.directViewHeight = 150;
      this.directViewTop = 0;
    }

    this.directContainerHeight = optionAreaHeight - directBottom;
  }

  _getWindowPortraitHeight() {
    if (this.winPortraitHeight == undefined) {
      this._initHeightValues();
    }

    return this.winPortraitHeight;
  }

  _getVideoPortraitHeight() {
    if (this.videoPortraiHeight == undefined) {
      this._initHeightValues();
    }

    return this.videoPortraiHeight;
  }

  _getDirectionContainerHeight() {
    if (this.directContainerHeight == undefined) {
      this._initHeightValues();
    }
    return this.directContainerHeight;
  }

  _getDirectionViewHeight() {
    if (this.directViewHeight == undefined) {
      this._initHeightValues();
    }
    return this.directViewHeight;
  }

  _getDirectionViewTop() {
    if (this.directViewTop == undefined) {
      this._initHeightValues();
    }
    return this.directViewTop;
  }

  _getVideoAreaHeight() {
    if (this.state.fullScreen) {
      return "100%";
    } else {
      return this._getVideoPortraitHeight();
    }
  }

  _renderVideoLayout() {
    let videoLayoutStyle;
    if (this.state.fullScreen) {
      videoLayoutStyle = {
        backgroundColor: 'black',
        width: "100%",
        height: "100%",
        position: "relative"
      };
    } else {
      let videoHeight = this._getVideoPortraitHeight();
      let containerHeight = videoHeight + this._getTitleBarPortraitHeight();//视频区域的高度 == 标题栏+状态栏+视频高度
      videoLayoutStyle = {
        display: "flex",
        position: "relative",
        zIndex: 0,
        backgroundColor: "black",
        // backgroundColor: "#ffffff",
        flexDirection: 'column',
        // width: this.winPortraitWidth,
        width: "100%",
        // height: containerHeight
        height: videoHeight
      };
    }

    return (
      <View style={ videoLayoutStyle }>
        { this._renderVideoView() }

        { this._renderAngleView() }
        { this._renderDefaultBgView() }

        { this._renderPowerOffView() }
        { this._renderRecordTimeView() }
        { this._renderErrorRetryView() }
        { this._renderLoadingView() }
        { this._renderSnapshotView() }
        { this._renderPauseView() }
        { this._renderDirectionEndHintView() }

        {/*{this._renderTitleView()}*/ }
        { this._renderBackButton() }

        { this._renderFloatingButtonsGroup() }
        { this._renderLandscapeButtonsGroup() }
        {/*{this._renderLandscapeDirectionView()}*/ }
        { this._renderLandscapeCallView() }

        { this._renderResolutionDialog() }
        {/*{ this._renderPanoViewDialog() }*/}
        { this._renderTargetPushView() }

        {/* {this._renderTestView()} */ }
      </View>
    );
  }

  // 这里是停止转动
  _renderDirectionEndHintView() {
    return null;// CHUANGMI-8497
  }


  _renderSleepNotifyDialog() {

    return (
      <CommonMsgDialog
        ref={ (ref) => {
          this.sleepDialog = ref;
        } }
        visible={ false }
        title={ CameraConfig.isSupportPhysicalCover(Device.model) ? LocalizedStrings["physical_cover_title"] : LocalizedStrings["sleep_off_title"] }
        text={ LocalizedStrings["sleep_off_subtitle"] }
        onConfirmPress={ () => {
          this.sleepDialog.hide();
        } }
      >
      </CommonMsgDialog>

      //   <MJDialog
      //     ref = { component=>{ this.dialog = component; } }
      //     outSideDisappear = { true } //是否点击半透明区域让对话框消失;
      //     contentStyle = { styles.dialog }
      //     position = "bottom" // 对话框内容区域布局对齐方式:"center","top","bottom", 默认值是:"center"
      //     height = { PixelRatio.getPixelSizeForLayoutSize(50) }
      //     isHidden = { this.state.isDialogShow } // 对话框默认是否隐藏,默认为false
      //     isStatusBarHidden = { true } //状态栏是否处于显示状态，默认为false;
      //     margin = { PixelRatio.getPixelSizeForLayoutSize(5) }>
      //       <View style={ styles.dialog }>
      //           <Text style={ styles.title }>关闭摄像机</Text>
      //           <Text style={ styles.content }>摄像机将停止工作和记录视频信息</Text>
      //           <View style = { styles.dialogContent }>
      //               <Text style={ styles.ok }
      //               >知道了</Text>
      //           </View>
      //         </View>
      // </MJDialog>
    );

  }

  _renderLogUploaderDialog() {
    return (
      <MessageDialog
        message={ this.state.logDialogContent }
        cancelable={ false }
        buttons={ [
          {
            text: LocalizedStrings['action_cancle'],
            callback: () => {
              feedbackLogUploaderStatus(2);
              this.setState({ showLogDialog: false });
            }
          },
          {
            text: LocalizedStrings['action_confirm'],
            callback: () => {
              feedbackLogUploaderStatus(1);
              this.setState({ showLogDialog: false });
            }
          }

        ] }
        onDismiss={ () => {
          console.log('onDismiss');
          this.setState({ showLogDialog: false });
        } }
        visible={ this.state.showLogDialog }/>
    );
  }

  // 这里是弹出对话框
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // status == 2 摄像头
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else if (this.state.permissionRequestState == 1) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    }
    return (
      // <AbstractDialog

      <MessageDialog
        title={ LocalizedStrings["tips"] }
        message={ message }
        modalStyle={ { width: "100%" } }
        messageStyle={ {
          fontSize: 14
        } }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ] }
        onDismiss={ () => {
          this.setState({ showPermissionDialog: false });
        } }
        visible={ this.state.showPermissionDialog }/>
    );

  }

  _renderRecordTimeView() {
    if (!this.state.isRecording) {
      return null;
    }
    let recordTimerBarHeight = 24;
    // let top = this.state.fullScreen ? (kScreenHeight > 600 ? 320 : 200) : (kScreenHeight > 600 ? (this._getVideoAreaHeight() - 10 - recordTimerBarHeight) : 100);

    let containerStyle = {
      position: "absolute",
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      alignSelf: "center",
      backgroundColor: "#000000CC", // 0.8 opacity
      borderRadius: 20,
      width: 90,
      height: recordTimerBarHeight
    };

    if (this.state.fullScreen) {
      containerStyle.top = 30;
    } else {
      // containerStyle.bottom = (kScreenHeight > 600 ? (this._getVideoAreaHeight() - 10 - recordTimerBarHeight) : 100);
      containerStyle.top = this._getStatusBarHeight() + 30;
    }

    let seconds = this.state.recordTimeSeconds;
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60 % 24);

    // if (Platform.OS === "ios") {
    //   this.lastRecordTime = `${ minute > 9 ? minute : `0${ minute }` }:${ second > 9 ? second : `0${ second }` }`;
    // } else {
    //   this.lastRecordTime = `${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ second > 9 ? second : `0${ second }` }`;
    // }
    // this.lastRecordTime = `${ minute > 9 ? minute : `0${ minute }` }:${ second > 9 ? second : `0${ second }` }`;
    this.lastRecordTime = `${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ second > 9 ? second : `0${ second }` }`;

    return (
      <View
        style={ containerStyle }>
        <View
          accessibilityLabel={ this.state.fullScreen ? DescriptionConstants.zb_43 : DescriptionConstants.zb_38 }
          style={ {
            backgroundColor: "#F43F31FF",
            borderRadius: 5,
            width: 5,
            height: 5,
            marginRight: 2
          } }/>
        <Text style={ {
          color: "#ffffff",
          fontSize: kIsCN ? 13 : 11,
          fontWeight: "bold",
          textAlign: "center",
          textAlignVertical: "center",
          lineHeight: 24,
          marginLeft: 2
        } }
        >{ this.lastRecordTime }</Text>
      </View>


    );
  }

  _renderDefaultBgView() {
    if (!this.state.showDefaultBgView) {
      return null;
    }

    if (Platform.OS === "ios") {
      if (!this.state.bgImgUri) return null;
      //   return null; // 只有安卓在渲染第一帧之前显示CameraRenderView的背景色，ios会在背景色上显示黑色视频区域，不需要黑色背景
    }

    // let imgSource = { uri: this.state.bgImgUri };
    let viewHeight = this._getVideoAreaHeight();
    return (
      <View style={ {
        display: "flex",
        position: "absolute",
        bottom: 0,
        width: "100%",
        backgroundColor: "black",
        height: viewHeight
      } }
      >

        { this.state.bgImgUri ? <Image style={ { width: "100%", height: "100%" } }
                                       source={ { uri: this.state.bgImgUri } }
        /> : null }

      </View>
    );
  }

  _renderPauseView() {
    if (!this.state.showPauseView || this.state.showErrorView) {
      return null;
    }
    let viewHeight = this._getVideoAreaHeight();
    return (
      <View style={ {
        display: "flex",
        position: "absolute",
        bottom: 0,
        width: "100%",
        height: viewHeight,
        alignItems: "center",
        justifyContent: "center",
        zIndex: 10
      } }
      >
        <ImageButton
          style={ { width: 64, height: 64 } }
          source={ require("../../Resources/Images/home_icon_pause_normal.png") }
          onPress={ () => {
            // StorageKeys.IS_DATA_USAGEE_WARNING = false //wifi下
            if (this.networkType === "CELLULAR") {
              this.skipDataWarning = true;
            }
            Service.smarthome.reportLog(Device.model, "on pause clicked");
            this._stopAll();
            console.log("pauseIcon clicked");
            this.connRetry = 2;
            this.queryNetworkJob();
          } }
        />
      </View>
    );
  }

  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }
    // first change statusBar
    // 切换到其他页面时候强制设置light-content 会导致浅色页面statusbar看不到
    if (this.isPageForeGround && this.isPluginForeGround) { // 在前台时才会显示
      // StatusBar.setBarStyle('light-content');
    }
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }

    // StatusBar.setBarStyle('dark-content');
    // second get statusBar height;
    let containerHeight = this._getTitleBarPortraitHeight();
    let statusBarHeight = this._getStatusBarHeight();

    let bgColor;
    let txtColor;
    let gradientColors;
    let iconBack;
    let iconBackPre;
    let iconMore;
    let iconMorePre;
    let showGradient = false;
    gradientColors = ['#00000099', '#00000000'];
    // if (Platform.OS == 'android') {
    //   gradientColors = ['#FFFFFF99', '#00000000'];
    // }

    let videoCovered = false;
    if (this.state.showErrorView || this.state.showPoweroffView || this.state.showPauseView ||
      this.state.showDefaultBgView || this.state.whiteTitleBg) {
      videoCovered = true;
    }

    bgColor = "transparent";
    if (this.videoPortraitScale <= 1.03 || videoCovered) {
      bgColor = "#ffffff";
      txtColor = "black";
      iconBack = !this.state.darkMode ? require("../../Resources/Images/icon_back_black.png") : require("../../Resources/Images/icon_back_black_nor_dark.png");
      iconBackPre = !this.state.darkMode ? require("../../Resources/Images/icon_back_black.png") : require("../../Resources/Images/icon_back_black_nor_dark.png");
      iconMore = !this.state.darkMode ? require("../../Resources/Images/icon_more_black_nor.png") : require("../../Resources/Images/icon_more_black_nor_dark.png");
      iconMorePre = !this.state.darkMode ? require("../../Resources/Images/icon_more_black_pre.png") : require("../../Resources/Images/icon_more_black_pre_dark.png");
      if (this.isPageForeGround) {
        this.state.darkMode ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
      }
    } else {
      showGradient = true;
      txtColor = "#ffffff";
      iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
      iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");
      iconMore = require("../../Resources/Images/icon_more.png");
      iconMorePre = require("../../Resources/Images/icon_more_pres.png");
      if (this.isPageForeGround) {//去往其他页面后 直播页面还有偶现的setState导致调用刷新了状态栏颜色, 未知原因
        StatusBar.setBarStyle('light-content');
      }
    }

    let titleBarStyle = {
      display: "flex",
      position: "absolute",
      top: 0,
      height: containerHeight,
      width: "100%",
      flexDirection: "column",
      backgroundColor: bgColor,
      zIndex: 1,
      alignItems: "center"
    };

    const containerStyle = {
      zIndex: 1,
      position: "relative",
      marginTop: statusBarHeight,
      height: navigationBarHeightFat,
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      paddingLeft: 12,
      paddingRight: 12
    };

    const gradientStyle = {
      position: "absolute",
      top: 0,
      width: "100%",
      height: "100%"
    };

    const textContainerStyle = {
      display: "flex",
      flexDirection: "column",
      height: "100%",
      width: kWindowWidth - (40 * 3),
      paddingHorizontal: 20,
      flexGrow: 1,
      position: "relative",
      alignSelf: 'stretch',
      justifyContent: 'center'

    };

    const titleTextStyle = {
      fontSize: kIsCN ? 16 : 14,
      textAlignVertical: 'center',
      textAlign: 'center'
    };

    const subTitleTextStyle = {
      fontSize: kIsCN ? 12 : 10,
      lineHeight: 17,
      fontFamily: 'MI-LANTING--GBK1-Light',
      textAlignVertical: 'center',
      textAlign: 'center'
    };

    const titleColor = { color: txtColor };
    const subTitleColor = { color: txtColor };
    let imageBtnStyle = {
      width: 40,
      height: 40
    };

    // if (this.state.darkMode) {
    //   imageBtnStyle.tintColor = IMG_DARKMODE_TINT;
    // }

    // {{ width: iconSize, height: iconSize, position: "absolute", tintColor:"#ddddddFF" }

    return (
      <View style={ titleBarStyle }>
        { showGradient ? <LinearGradient colors={ gradientColors } style={ gradientStyle }/> : null }
        <View
          style={ containerStyle }>
          <View
            style={ { width: 40, height: 40 } }>
            <ImageButton
              style={ imageBtnStyle }
              source={ iconBack }
              highlightedSource={ iconBackPre }
              accessibilityLabel={ `${ DescriptionConstants.zb_29 }` }
              onPress={ () => {
                if (this.state.isCalling) {
                  Toast.success("camera_speaking_block");
                  return;
                }
                if (this.state.isRecording) {
                  Toast.success("camera_recording_block");
                  return;
                }
                this._stopAll();
                Package.exit();
              } }
            />
          </View>

          <View style={ textContainerStyle }>

            <Text
              accessible={ true }
              accessibilityLabel={ DescriptionConstants.zb_27 + (Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : "") }
              importantForAccessibility="no-hide-descendant"
              numberOfLines={ 1 }
              ellipsizeMode={ "tail" }
              style={ [titleTextStyle, titleColor] }

            >
              { Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : "" }
            </Text>
            <Text
              accessible={ true }
              accessibilityLabel={ DescriptionConstants.zb_28 + (this.state.bps > 1024
                ? `${ Number.parseInt(this.state.bps / 1024) } KB/S`
                : `${ Number.parseInt(this.state.bps) } B/S`) }
              numberOfLines={ 1 }
              ellipsizeMode={ "tail" }
              style={ [subTitleTextStyle, subTitleColor] }
            >
              {
                !this.isPowerOn ? `0 B/S` : this.state.bps > 1024
                  ? this.state.isCruising ? `${ Number.parseInt(this.state.bps / 1024) } KB/S ${ LocalizedStrings.cruise_ing }` : `${ Number.parseInt(this.state.bps / 1024) } KB/S`
                  : this.state.isCruising ? `${ Number.parseInt(this.state.bps) } B/S ${ LocalizedStrings.cruise_ing }` : `${ Number.parseInt(this.state.bps) } B/S`
              }
            </Text>

          </View>

          <View style={ { width: 40, height: 40, position: "relative" } }>
            {
              (!Device.isReadonlyShared && (this.state.showRedDot || this.state.showNasRedDot)) ?
                <Image
                  style={ { width: 40, height: 40, position: "absolute" } }
                  source={ require("../../Resources/Images/icon_dot.png") }
                />
                : null
            }

            {
              Device.isReadonlyShared ?
                null
                :
                <ImageButton
                  style={ imageBtnStyle }
                  source={ iconMore }
                  highlightedSource={ iconMorePre }
                  accessibilityLabel={ DescriptionConstants.zb_30 }
                  onPress={ () => {
                    if (this.state.isCalling) {
                      Toast.success("camera_speaking_block");
                      return;
                    }
                    if (this.state.isRecording) {
                      Toast.success("camera_recording_block");
                      return;
                    }
                    AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip);
                    this.delayPause = true; // willBlur执行后截屏会失败，因此进入设置需要延时调onPause()
                    this.props.navigation.navigate('Setting', {
                      'title': '设置',
                      hasFirmwareUpdate: this.state.showRedDot,
                      hasNasTips: this.state.showNasRedDot,
                      vip: this.isVip,
                      inCloseWindow: this.inWindow,
                      vipStatus: this.mVipStatus
                    });
                    this.setState({ showRedDot: false });
                    TrackUtil.reportClickEvent('Camera_Setting_ClickNum');
                  } }
                />
            }

          </View>
        </View>
      </View>
    );
  }

  _renderVideoView() {

    let videoStyle;
    if (this.state.fullScreen) {
      videoStyle = {
        position: "absolute",
        bottom: 0,
        width: "100%",
        height: "100%"
      };
    } else {
      // let videoHeight = this._getVideoPortraitHeight() + this._getTitleBarPortraitHeight() * 2;//视频内容高度实际：两倍标题栏+视频高度
      let videoHeight = this._getVideoPortraitHeight();
      let bottom = -this._getTitleBarPortraitHeight(); // 由于是绝对布局，需要距离父布局底部 -的标题栏高度，这样子做，可以控制视频缩放的时候正好在视频区域中心点缩放
      // videoHeight = kWindowWidth*3/2
      if (this.state.rotation == 90) {
        videoStyle = {
          // rotation 90
          position: "absolute",
          width: "100%",
          height: videoHeight,
          transform: [{ rotate: '270deg' }]
        };
      } else if (this.state.rotation == 270) {
        // rotation 270
        videoStyle = {
          position: "absolute",
          width: "100%",
          height: videoHeight,
          transform: [{ rotate: '90deg' }]
        };
      } else {
        // rotation 0/180
        videoStyle = {
          position: "absolute",
          // bottom: bottom, // 为了在自适应时没有白边漏出
          width: "100%",
          height: videoHeight
        };
      }
    }
    // console.log("return CameraRenderView: " + ((Platform.OS =="android" && this.evenLockScreen >0) && (!this.state.restoreOriFinished || !this.state.restoreOriFinished2)) +  ", evenLockScreen: " + this.evenLockScreen)
    let useWhiteBackground = this.state.darkMode || this.state.fullScreen ? false : this.state.isWhiteVideoBackground;
    useWhiteBackground = false;
    return (
      <CameraRenderView
        ref={ (ref) => {
          this.cameraGLView = ref;
          // if (this.cameraGLView && Platform.OS === "ios" && !this.hasSetPlayerType) {
          //   NativeModules.MHCameraOpenGLViewManager.setGeneralIjkPlayerEnable(false, Device.deviceID);
          //   this.hasSetPlayerType = true;
          // }
        } }
        isAudioCallInCommunication={{isAudioCallInCommunication:this.state.isAudioCallInCommunication,did:Device.deviceID}}
        maximumZoomScale={ 6.0 }
        style={ [videoStyle] }
        // videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        // audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        videoCodec={ MISSCodec.MISS_CODEC_VIDEO_H265 }
        audioCodec={ MISSCodec.MISS_CODEC_AUDIO_OPUS }
        audioRecordSampleRate={ CameraConfig.getCameraAudioSampleRate(Device.model) }
        audioRecordChannel={ MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO }
        audioRecordDataBits={ MISSDataBits.FLAG_AUDIO_DATABITS_16 }
        audioRecordCodec={ MISSCodec.MISS_CODEC_AUDIO_OPUS }
        fullscreenState={ this.state.fullScreen }
        scale={this.state.savedVideoScale}
        videoRate={ this.rateModel() }
        correctRadius={ CameraConfig.getCameraCorrentParam(Device.model).radius }
        osdx={ this.state.isWatermarkEnable ? CameraConfig.getCameraCorrentParam(Device.model).osdx : 0 }
        osdy={ this.state.isWatermarkEnable ? CameraConfig.getCameraCorrentParam(Device.model).osdy : 0 }
        useLenCorrent={ this.state.useLenCorrent }
        onVideoClick={ this._onVideoClick.bind(this) }
        onScaleChanged={ this._onVideoScaleChanged.bind(this) }
        onPTZDirectionCtr={ this._onVideoPTZDirectionCtr.bind(this) }
        did={ Device.deviceID }
        isFull={ true }
        playRate={ 24 }
        whiteBackground={ useWhiteBackground }
        // recordingVideoParam={{ width: 1920, height: 1080 }}
        recordingVideoParam={ {
          ...CameraConfig.getRecordingVideoParam(Device.model),
          isInTimeRecord: false,
          hasRecordAudio: true,
          iosConstantFps: 1,
          fps: -1
        } }
        enableAIFrame={ this.state.enableAIFrame }
        accessible={ true }
        accessibilityLabel={ DescriptionConstants.zb_54 }
        useZoomLens={ false }
        // chanPositionParams={
        //   {
        //     "chan": 0, //int 默认为0,如果只有单流用0即可
        //     "scale": this.state.fullScreen ? 1.0 : this.state.videoScale, //float 缩放倍数
        //     "offsetX": this.state.offsetX, //float
        //     "offsetY": this.state.offsetY //float
        //   }
        // }
        // onPositionChanged={ (res) => {
        //   // console.log("=========onPositionChanged", res);
        // } }

      >
      </CameraRenderView>
    );
  }

  rateModel() {
    if (CameraConfig.deviceFrameRate(Device.model)) {
      return 20;
    }
    return 15;
  }

  setTargetPushViewShown() {
    if (this.state.isNewDevice) {
      StorageKeys.IS_SECURITY_CODE_TIP_SHOWN = true;
    } else {
      StorageKeys.IS_TARGET_PUSH_SHOWN = true;
    }
  }

  _renderTargetPushView() {
    if (!this.state.showTargetPushView || this.shownTarget) {
      return null;
    }
    let ViewStyle = {
      position: "absolute",
      width: "100%",
      // top: this._getTitleBarPortraitHeight() + 13,
      top: this.mOri === "PORTRAIT" ? this._getTitleBarPortraitHeight() + 13 : 13,
      alignItems: "center",
      justifyContent: "center"
    };
    let containerStyle = {
      position: "absolute",
      top: 0,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 10,
      borderWidth: 0.5,
      borderColor: "rgba(255,255,255,0.1)",
      flexDirection: "row",
      backgroundColor: "#ffffff",
      shadowRadius: 8,
      marginHorizontal: 20,
      paddingStart: 2,
      paddingTop: 6,
      paddingBottom: 6,
      shadowOpacity: 0.15,
      shadowOffset: { width: 0, height: 2 },
      shadowColor: "#ffffff"
    };
    let title = null;
    let titleh5 = null;
    let subtitle = null;
    switch (this.targetpushItem) {
      case "1":
        title = LocalizedStrings["targetPushTitle_nightModeClose"];
        titleh5 = `https://home.mi.com/webapp/content/camera-tips.html#/faqDetail/248859482000000002`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "2":
        title = LocalizedStrings["targetPushTitle_nightModeOpen"];
        titleh5 = `https://home.mi.com/webapp/content/camera-tips.html#/faqDetail/565203084000000002`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "2_sd":
        title = LocalizedStrings["sdcard_notfull_title_tips"];
        titleh5 = `Native_SDCardSetting`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "3":
        title = LocalizedStrings["targetPushTitle_DWROpen"];
        titleh5 = `https://home.mi.com/views/article.html?articleId=512091754000000001`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle1"];
        break;
      case "3_sd":
      case "4_sd":
        title = LocalizedStrings["sdcard_status_error"];
        titleh5 = `https://home.mi.com/views/article.html?articleId=10139525000000001`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "5":
        title = LocalizedStrings["targetPushTitle_securityCode"];
        titleh5 = `Native_securityCodeSetting`;
        subtitle = null;
        break;
      case "6":
        title = LocalizedStrings["targetPush_sdcard_format"];
        titleh5 = `Native_SDCardFormatSetting`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "7":
        title = LocalizedStrings["targetPushTitle_weakNetWork"];
        titleh5 = `Native_weakNetWorkSetting`;
        // subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "8_sd":
      case "10_sd":
      case "11_sd":
        title = LocalizedStrings[`sdcard_format_title_tips_${ this.sdcardCode }`];
        titleh5 = `Native_SDCardFormatSetting`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      case "9_sd":
        title = LocalizedStrings["sdcard_small_title_tips"];
        titleh5 = `Native_SDCardSetting`;
        subtitle = LocalizedStrings["targetPushTitle_subtitle"];
        break;
      default:
        console.log(`TargetPushError`);
    }
    if (!title) {
      console.log("=================", title, this.targetpushItem);
      return null;
    }
    title = `${ title } `;
    return (
      <View style={ ViewStyle }>
        <View style={ containerStyle }>
          {/*<Text style={ { fontSize: kIsCN ? 12 : 10, color: "#4c4c4c", marginLeft: 5 } }*/}
          {/*      onPress={ subtitle ? null : () => this._showTargetPush(titleh5) }> { `${ title }` }<Text*/}
          {/*  style={ { fontSize: kIsCN ? 12 : 10, color: "#32bac0" } }*/}
          {/*  onPress={ () => this._showTargetPush(titleh5) }>{ subtitle }</Text></Text>*/}
          { this.targetpushItem != "7"
            ?
            <Text style={{ fontSize: kIsCN ? 12 : 10, color: "#4c4c4c", marginLeft: 5 }} onPress={subtitle ? null : () => this._showTargetPush(titleh5)}> {`${title}`}<Text style={{ fontSize: kIsCN ? 12 : 10, color: "#32bac0" }} onPress={() => this._showTargetPush(titleh5)} >{subtitle}</Text></Text>
            :
            <ParsedText
              style={{ fontSize: kIsCN ? 12 : 10, color: "#4c4c4c", marginLeft: 5 }}
              parse={
                [
                  { pattern: /\[1(.+?)\]/g, style: { fontSize: kIsCN ? 12 : 10, color: "#32bac0" }, onPress: () => this._showTargetPush(titleh5), renderText: this.renderText },
                ]
              }
              childrenProps={{ allowFontScaling: false }}
            >
              {title}
            </ParsedText>
          }
          <TouchableHighlight onPress={ () => this._closeTargetPush() }>
            <Image style={ { width: 18, height: 18, marginLeft: 8, marginRight: 9 } }
                   source={ require('../../Resources/Images/close.png') }></Image>
          </TouchableHighlight>
        </View>
      </View>
    );
  }
  renderText(matchingString, matches) {
    let find = '\\[|\\]|1|2';
    let re = new RegExp(find, 'g');
    return matchingString.replace(re, '');
  }
  verticalScreenTab() { //切换竖屏
    const initial = Orientation.getInitialOrientation();
    if (initial === 'PORTRAIT') {//当为横屏时
      // 当前非竖屏
      Orientation.lockToPortrait();
    }
  }

  _showTargetPush(h5) {
    if (h5.includes("weakNetWorkSetting")) {
      this.qulityToolPressed();
      return;
    }
    // 不跳转，弹出toast提示
    Toast.success('camera_speaking_block');

  }

  _closeTargetPush() {
    this.shownTarget = true;
    this.setState({
      showTargetPushView: false
    });
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }
    // 这里是视频的缩略图
    let recordItem = (
      <LinearGradient colors={ ['#00000000', '#00000077'] }
                      style={ { position: 'absolute', bottom: 0, width: "100%", height: "30%", borderRadius: 4 } }>
        <View style={ {
          display: "flex",
          flexDirection: "row",
          bottom: 2,
          left: 6,
          position: "absolute",
          alignItems: "center"
        } }>
          <Image style={ { width: 10, height: 10 } }
                 source={ require("../../Resources/Images/icon_snapshot_camera_play.png") }></Image>
          <Text style={ {
            fontSize: kIsCN ? 12 : 10,
            fontWeight: "bold",
            color: "white",
            marginLeft: 5
          } }>{ this.lastRecordTime }</Text>
        </View>
      </LinearGradient>
    );

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;

    let containerStyle;
    if (this.state.fullScreen) {
      let toLeft = 25;
      let screenWidth = this._getWindowPortraitHeight();
      if (Platform.OS === "ios" && screenWidth >= 800) {
        toLeft = 80;
      }

      containerStyle = {
        position: "absolute",
        top: kWindowWidth < 400 ? 100 : 70,
        left: 40,
        width: sWidth,
        height: sHeight,
        borderRadius: 4,
        borderWidth: 1.5,
        borderColor: "xm#ffffff"
      };
      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }
    } else {
      containerStyle = {
        position: "absolute",
        // bottom: this._getVideoAreaHeight() - sPadding - sHeight,
        top: 100,
        left: 20,
        width: sWidth,
        height: sHeight,
        borderRadius: 4,
        borderWidth: 1.5,
        borderColor: "xm#ffffff",
        backgroundColor: "transparent",
        zIndex: 100
      };
    }
    return (
      <View style={ containerStyle }
      >
        <ImageButton

          accessibilityLabel={ this.isForVideoSnapshot ? DescriptionConstants.hk_2_6 : this.state.fullScreen ? DescriptionConstants.zb_18 : DescriptionConstants.zb_17 }
          style={ { width: "100%", height: "100%", borderRadius: 4 } }
          source={ (this.state.screenshotPath == "") ? null : { uri: `file://${ Host.file.storageBasePath }/${ this.state.screenshotPath }` } }
          fadeDuration={ 0 }
          onPress={ () => {
            if (!this.canStepOut()) {
              return;
            }
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。

            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面");
              this.showLastVideo();
              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {

              console.log("点击了缩略图，跳转到图片页面", { uri: `file://${ Host.file.storageBasePath }/${ this.state.screenshotPath }` });
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }
            this.isForVideoSnapshot = false;


            // todo jump to album activity
          } }
        />


        { this.isForVideoSnapshot ? recordItem : null }

      </View>
    );
  }

  showLastImage() {
    if (Platform.OS === 'ios') {// IOS系统本身拥有APP获取的权限，当APP无法获取截取的照片时会自动提示操作失败
      AlbumHelper.getAlbumFiles()
        .then(() => {
          //当IOS获取权限时
          this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
        })
        .catch((error) => {
          Toast._showToast(`${ LocalizedStrings["action_failed"] }3`);
        });
    } else {
      this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
    }
  }

  showLastVideo() {
    if (Platform.OS === 'ios') { // IOS系统本身拥有APP获取的权限，当APP无法获取截取的照片时会自动提示操作失败
      AlbumHelper.getAlbumFiles()
        .then(() => {
          // 当IOS获取权限时
          this.props.navigation.navigate("AlbumVideoViewPage", {
            videoName: this.state.videoName,
            preOri: this.state.fullScreen ? "landscape" : "portrait"
          });
        })
        .catch((error) => {
          Toast._showToast(`${ LocalizedStrings["action_failed"] }`);
        });
    } else {
      this.props.navigation.navigate("AlbumVideoViewPage", {
        videoName: this.state.videoName,
        preOri: this.state.fullScreen ? "landscape" : "portrait"
      });
    }
  }

  showLastMediaFileInAlbum(isImage) {
    let medType = isImage ? 1 : 2;

    AlbumHelper.getAlbumFiles()
      .then((result) => {
        console.log(`why!, getAlbumFiles, result: ${ JSON.stringify(result) }`);
        // 肯定是array了
        let mediaArray = result.filter((item) => item.mediaType == medType);// 过滤所有的图片

        let mediaItem = null;
        if (mediaArray.length == 0) {
          Toast._showToast(LocalizedStrings["action_failed"]);
          this.props.navigation.goBack();// 操作失败
          // 索引出错。
          return;
        }
        mediaItem = mediaArray[0];
        let url = mediaItem.url;

        let data = { fileName: this.state.screenshotPath, filePath: url, fileUrl: url };
        NativeModules.MHCameraSDK.showAlbumMediaFile(Device.deviceID, JSON.stringify(data));
        // Service.miotcamera.showAlbumMediaFile(JSON.stringify(data));
      })
      .catch((error) => {
        console.log(error);
        Toast._showToast(LocalizedStrings["action_failed"]);
      });

  }

  // 这里是直播中的小窗口
  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }

    let sPadding = 20;
    let bottom = this.state.fullScreen ? (kScreenHeight > 600 ? 250 : 180) : (kScreenHeight > 600 ? (this._getVideoAreaHeight() - 28 - sPadding) : 80);// 28 is angle view's height
    let left = this.state.fullScreen ? 55 : sPadding;
    let top = this.state.fullScreen && Host.isPad ? this._getStatusBarHeight() + 246 : this._getStatusBarHeight() + 66;

    let angleStyle = {
      position: "absolute",
      left: left,
      top: top

    };

    return (
      <View style={ angleStyle }>
        <RectAngleView
          ref={ (ref) => {
            this.angleView = ref;
          } }
          angle={ this.state.angle }
          elevation={ this.state.elevation }
          scale={ this.state.videoScale }
          showScale={ this.state.angleViewShowScale }
          accessible={ true }
          accessibilityLabel={ DescriptionConstants.zb_39.replace('1', this.state.videoScale) }
        />
      </View>
    );
  }

  _hidePlayToolBarLater() {
    let tTimer = 10000;
    clearTimeout(this.showPlayToolBarTimer);
    if (VersionUtil.judgeIsV1(Device.model) && this.state.fullScreen) {
      tTimer = 50000;
    }
    this.showPlayToolBarTimer = setTimeout(() => {
      this.setState({ showPlayToolBar: false });
    }, tTimer);
  }

  _renderPowerOffView() {
    if (!this.state.showPoweroffView) {
      return null;
    }
    let height = this._getVideoAreaHeight();

    return (
      <View style={ { width: "100%", height: height, position: "absolute", bottom: 0 } }>

        <TouchableWithoutFeedback style={ { width: "100%", height: "100%", position: "absolute", bottom: 0 } }
                                  onPress={ () => {
                                    // this._toggleSleep(false);
                                    this.setState({ showPlayToolBar: true });
                                    if (this.showPlayToolBarTimer) {
                                      clearTimeout(this.showPlayToolBarTimer);
                                      this.showPlayToolBarTimer = null;
                                    }
                                    if (!this.state.fullScreen) {
                                      this._hidePlayToolBarLater();
                                    }
                                  } }
        >
          <View style={ {
            backgroundColor: "black",
            width: "100%",
            height: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center"
          } }
          >
            <Image
              style={ { width: 54, height: 54 } }
              source={ require("../../Resources/Images/icon_camera_sleep.png") }/>
            <Text
              style={ { marginTop: 10, fontSize: kIsCN ? 14 : 12, color: "#bfbfbf" } }>
              { LocalizedStrings[this.getPowerOffString()] }
            </Text>
          </View>
        </TouchableWithoutFeedback>
      </View>

    );
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    if (!Device.isOnline && this.state.errTextString != LocalizedStrings['device_offline']) {
      this.setState({ errTextString: LocalizedStrings['device_offline'] });
    }

    let viewHeight = this._getVideoAreaHeight();

    let sleepButton = (
      <View
        style={ {
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10,
          height: 26,
          justifyContent: "center"

        } }>
        <Text style={ {
          color: "#fff",
          fontSize: kIsCN ? 12 : 10,
          textAlign: 'center',
          textAlignVertical: 'center',
          paddingLeft: 17,
          paddingRight: 17
        } }
        >{ LocalizedStrings['offline_see_help'] }</Text>
      </View>
    );
    let buttonReConnectItem = (
      <View
        style={ {
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10,
          height: 26,
          justifyContent: "center"
        } }>
        <Text style={ {
          color: "#fff",
          fontSize: kIsCN ? 12 : 10,
          textAlign: 'center',
          textAlignVertical: 'center',
          paddingLeft: 17,
          paddingRight: 17
        } }
        >{ LocalizedStrings['reconnect_button_text'] }</Text>
      </View>
    );

    let powerOfflineItem = (
      <TouchableOpacity
        style={ { display: "flex", alignItems: "center" } }
        onPress={ () => {
          this.refs.powerOfflineDialog.show();
        } }>
        { sleepButton }
      </TouchableOpacity>
    );
    let powerOfflineText = (
      <Text
        style={ { marginTop: 5, fontSize: kIsCN ? 12 : 10, color: "#bfbfbf" } }
      >
        { this.state.lastOfflineTime }
      </Text>
    );
    let noNetworkItem = (
      <View style={ { display: "flex", flexDirection: "row" } }>
        <TouchableOpacity
          style={ { display: "flex", alignItems: "center", paddingRight: 8 } }
          onPress={ () => {
            this.setState({ showErrorView: false, bgImgUri: null });
            Service.smarthome.reportLog(Device.model, "on error Retry");
            this._stopAll();
            console.log("error button clicked");
            this.queryNetworkJob();
          } }>
          { buttonReConnectItem }
        </TouchableOpacity>
        <TouchableOpacity
          style={ { display: "flex", alignItems: "center", paddingLeft: 8 } }
          onPress={ () => {
            this.refs.noNetworkDialog.show();
          } }>
          { sleepButton }
        </TouchableOpacity>

      </View>

    );

    const errIcons = [
      require("../../Resources/Images/icon_connection_failure.png"),
      require("../../Resources/Images/icon_camera_offline.png"),
      require("../../Resources/Images/icon_camera_fail.png")
    ];

    let errIconIndex = 0;
    if (!Device.isOnline) {
      errIconIndex = 1;
    }
    return (
      <View
        style={ {
          zIndex: 7,
          position: "absolute",
          bottom: 0,
          backgroundColor: "black",
          width: "100%",
          height: viewHeight,
          display: "flex",
          justifyContent: "center",
          alignItems: "center"
        } }
      >
        {
          this.state.fullScreen ?
            <View style={ {
              position: "absolute",
              width: "100%",
              height: 80,
              top: (Host.isPad && Host.isAndroid) ? 35 : 5
            } }>
              { this.state.fullScreen ? this._renderFullScreenBackButton() : null }
            </View>
            :
            null
        }

        <TouchableOpacity
          style={ { display: "flex", alignItems: "center" } }
          onPress={ () => {
            this.setState({ showErrorView: false, bgImgUri: null });
            Service.smarthome.reportLog(Device.model, "on error Retry");
            this._stopAll();
            console.log("error button clicked");
            this.queryNetworkJob();
          } }// 走重新播放的逻辑,如果是断线了  会走重连的逻辑的}
        >
          <Image
            style={ { width: 54, height: 54 } }
            source={ errIcons[errIconIndex] }/>
          <Text
            style={ {
              marginTop: 10,
              fontSize: kIsCN ? 12 : 10,
              color: "#ffffff",
              paddingHorizontal: 10,
              textAlign: "center",
              width: kWindowWidth - 60
            } }>

            { this.currentNetworkState == 0 ? LocalizedStrings['common_net_error'] : this.state.errTextString }{ Device.isOnline ? "" : (this.state.lastOfflineTime == "") ? "" : `, ${ this.state.lastOfflineTime }` }

          </Text>
          {/* {Device.isOnline ? null : powerOfflineText} */ }
        </TouchableOpacity>
        { (Device.isOnline ? noNetworkItem : null) }
        { Device.isOnline ? null : powerOfflineItem }
      </View>
    );
  }

  _renderLoadingView() {
    if (!this.state.showLoadingView || this.state.showPoweroffView) {
      return null;
    }

    let height = this._getVideoAreaHeight();
    let bgColor = "transparent";
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: height,
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: bgColor
    };
    let loadingTxt = this.isPhoneNetworkError ? LocalizedStrings['network_owner_wrong'] : this.isErrorToConnect ? LocalizedStrings['network_opposite_wrong'] : LocalizedStrings["camera_loading"]
    return (
      <TouchableOpacity
        style={ loadingViewStyle }
        onPress={ () => {
          const initial = Orientation.getInitialOrientation();
          if (initial === 'PORTRAIT') { // 当为横屏时
            this.setState(() => {
              return {
                showPlayToolBar: !this.state.showPlayToolBar
              };
            }, () => {
              this._hidePlayToolBarLater();
            });
          }
        }
        }
      >
        {
          this.state.fullScreen ?
            <View style={ {
              position: "absolute",
              width: "100%",
              height: 80,
              top: (Host.isPad && Host.isAndroid) ? 35 : 5
            } }>
              { this._renderFullScreenBackButton() }
            </View>
            :
            null
        }
        <LoadingView
          style={ { width: 54, height: 54 } }
        />
        <Text
          style={ { marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" } }>
          { loadingTxt }
        </Text>
      </TouchableOpacity>
    );
  }

  //竖屏模式下得tool button
  _renderFloatingButtonsGroup() {


    let resolutinIcon = [];

    let hfdIcon = this.isSupport2K ? require('../../Resources/Images/icon_2k_nor.png') : require('../../Resources/Images/icon_1080p_nor.png');
    let hfdDisIcon = this.isSupport2K ? require('../../Resources/Images/icon_2k_dis.png') : require('../../Resources/Images/icon_1080p_dis.png');

    switch (this.state.resolution) {
      case 1:
        resolutinIcon = [require('../../Resources/Images/icon_360p_nor.png'),
          require('../../Resources/Images/icon_360p_dis.png')
        ];
        break;
      case 3:
        resolutinIcon = [hfdIcon, hfdDisIcon];
        break;
      case 0:
      default:
        resolutinIcon = this.state.isNoneChinaLand ? [require('../../Resources/Images/icon_auto_en_nor.png'),
          require('../../Resources/Images/icon_auto_en_dis.png')
        ] : [require('../../Resources/Images/icon_auto_zh_nor.png'),
          require('../../Resources/Images/icon_auto_zh_dis.png')
        ];
        break;
    }


    let positionRight = 22;
    let screenWidth = this._getWindowPortraitHeight();
    if (Platform.OS === "ios" && screenWidth >= 800) {
      positionRight = 85;
    }


    let gradientStyle;
    let containerStyle;
    let gradientColors;
    if (this.state.fullScreen) {
      gradientStyle =
        {
          position: "absolute",
          right: 0,
          width: "100%",
          height: (Host.isPad && Host.isAndroid) ? 110 : 80
        };
      containerStyle = {
        display: "flex",
        width: "100%",
        paddingRight: positionRight,
        flexDirection: "row",
        justifyContent: "flex-end",
        top: (Host.isPad && Host.isAndroid) ? 50 : 20
      };
      gradientColors = ['#00000077', '#00000000'];
    } else {
      gradientStyle =
        {
          position: "absolute",
          bottom: 0,
          width: "100%",
          height: 70
        };

      containerStyle = styles.videoControlBarPortrait;

      gradientColors = ['#00000000', '#00000077'];
    }

    if (this.state.showPlayToolBar) {

      let itemStyle = this.state.fullScreen ? styles.videoControlBarItemLandscape : styles.videoControlBarItem;
      return (

        // { display:'none' } doesn't work on Android, use { opacity: 0, height: 0 }
        <LinearGradient colors={ gradientColors }
                        style={ [gradientStyle, !this.state.showPlayToolBar ? { opacity: 0, height: 0 } : {}] }>
          <View style={ containerStyle }>

            {/* 目前的 - Lottie动画按钮 */ }
            { this.state.fullScreen ? this._renderFullScreenTitle() : null }
            {/*{this.state.fullScreen ? null : this._renderSleepToolButton(itemStyle)}*/ }
            {/*{ this._renderAudioToolButton(itemStyle) }*/}
            { this.state.fullScreen ? null : this._renderRecordToolButton(itemStyle) }
            { this.state.fullScreen ? null : this._renderSnapToolButton(itemStyle) }
            { this._renderQulityToolButton(itemStyle) }
            { this.state.fullScreen ? null : this._renderFullScreenToolButton(itemStyle) }

            {/* 以前的 - 普通按钮 */ }
            {/* {this.state.fullScreen ? null : this._renderVideoFloatButton(sleepIcons[sleepIndex])}
            {this._renderVideoFloatButton(audioIcons[audioIndex])}
            {this._renderVideoFloatButton(resolutionIcons[resolutionIndex])}
            {this.state.fullScreen ? null : this._renderVideoFloatButton(fullScreenIcons[fullScreenIndex])} */ }

            {/* {this._renderVideoFloatButton(fullScreenIcons[this.state.fullScreen ? 1 : 0])} */ }
            { this.state.fullScreen ? this._renderFullScreenBackButton() : null }
          </View>
        </LinearGradient>
      );
    } else {
      return (null);
    }
  }

  _renderFullScreenTitle() {
    let topMargin = 0;
    if (this.state.isRecording) {
      topMargin = -22;
    }
    let titleBarStyle = {
      display: "flex",
      position: "absolute",
      top: topMargin,
      height: 40,
      width: "100%",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center"
    };
    return <View style={ titleBarStyle }>
      <Text style={ { color: "#ffffff" } }>
        { this.state.isCruising ? `${ LocalizedStrings.cruise_ing }` : "" }
      </Text>
    </View>;
  }

  _renderSleepToolButton(style) {
    return (
      <MHLottieSleepToolButton
        style={ style }

        onPress={ () => {
          this._hidePlayToolBarLater();
          if (this.state.isSleep) {
            this._toggleSleep(false);
          } else {
            this._toggleSleep(true);
          }
          TrackUtil.reportClickEvent('Camera_Sleep_ClickNum');
        } }
        disabled={ this.state.isRecording && !this.state.isSleep }
        displayState={ MHLottieSleepToolBtnDisplayState.NORMAL }
        accessibilityLabel={ !this.state.isSleep ? DescriptionConstants.zb_1 : DescriptionConstants.zb_1_1 }
      />
    );

  }

  _renderAudioToolButton(style) {

    return (
      <MHLottieAudioToolButton
        style={ style }
        onPress={ () => {
          this._hidePlayToolBarLater();
          // 和Native一致，通话中不允许控制开启关闭声音
          if (this.state.isCalling) {
            Toast.success("camera_speaking_block");
            return;
          }
          if (this.state.isMute) {

            // 默认是这个状态，去开启声音
            // if (this.state.isCalling) {
            this.isAudioMuteTmp = false;
            // }
            this._toggleAudio(false);
            TrackUtil.reportClickEvent('Camera_OpenVolume_ClickNum');
          } else {
            if (this.state.isCalling) {
              Toast.fail("camera_speaking_block");
              return;
            }
            // if (this.state.isCalling) {
            this.isAudioMuteTmp = true;
            // }
            this._toggleAudio(true);
          }
        } }

        displayState={ this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL }
        landscape={ this.state.fullScreen }

        disabled={ this.state.isSleep }
        accessible={ true }
        accessibilityLabel={ this.state.isMute ? DescriptionConstants.zb_2 : DescriptionConstants.zb_2_1 }
        accessibilityState={ {
          disabled: this.state.isSleep
        } }
      />
    );
  }

  _renderRecordToolButton(style) {

    return (
      <MHLottieRecordToolButton
        style={ style }
        label="rnlabelRecord"
        description="rndescriptionRecord"
        onPress={ () => {
          if (this.state.isRecording) {
            this._stopRecord();
          } else {
            this._startRecord();
          }

        } }

        displayState={ this.state.isRecording ? MHLottieRecordBtnDisplayState.RECORDING : MHLottieRecordBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep || this.state.showErrorView) }

        darkMode={ this.state.darkMode }
        accessible={ true }
        accessibilityState={ {
          selected: this.state.isRecording,
          disabled: this.state.isSleep || this.state.showErrorView
        } }
        accessibilityLabel={ DescriptionConstants.zb_7 }
        testID={ this.state.isRecording ? '1' : '0' }
      />
    );
  }

  _renderSnapToolButton(style) {

    return (
      <MHLottieSnapToolButton
        style={ style }
        accessibilityState={ {
          disabled: this.state.isSleep || this.state.showErrorView
        } }
        accessibilityLabel={ !this.state.fullScreen ? DescriptionConstants.zb_6 : DescriptionConstants.zb_14 }
        label="rnlabelSnap"
        description="rndescriptionSnap"

        onPress={ () => {
          this.setState({ screenshotVisiblity: false, screenshotPath: null }, () => {
            this._startSnapshot();
          });
        } }

        displayState={ MHLottieSnapBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep || this.state.showErrorView) }

        darkMode={ this.state.darkMode }
      />
    );
  }


  _renderQulityToolButton(style) {
    let displayState = MHLottieQulityToolBtnDisplayState.AUTO;

    let accessibilityText = '';
    switch (this.state.resolution) {
      case 1:
        if (CameraConfig.isNormalFHD()) {
          displayState = MHLottieQulityToolBtnDisplayState.RSD;
          accessibilityText = LocalizedStrings["camera_quality_low"].replace("360", "").replace(" ", "");
        } else if (CameraConfig.isSupport480P(Device.model)) {
          displayState = MHLottieQulityToolBtnDisplayState.R480;
          accessibilityText = LocalizedStrings["camera_quality_low"].replace("360", "480");
        } else {
          displayState = MHLottieQulityToolBtnDisplayState.R360;
          accessibilityText = LocalizedStrings["camera_quality_low"];
        }
        break;
      case 2:
        if (CameraConfig.isNormalFHD()) {
          displayState = MHLottieQulityToolBtnDisplayState.RHD;
        } else {
          displayState = MHLottieQulityToolBtnDisplayState.R720;
        }
        break;

      case 3:
        if (CameraConfig.isNormalFHD()) {
          displayState = MHLottieQulityToolBtnDisplayState.RFHD;
        } else {
          displayState = this.isSupport3K ? MHLottieQulityToolBtnDisplayState.R3K : this.isSupport2K ? MHLottieQulityToolBtnDisplayState.R2K : (this.isSupport25K ? MHLottieQulityToolBtnDisplayState.R25K : MHLottieQulityToolBtnDisplayState.R1080);
        }
        accessibilityText = this.isSupport2K ? LocalizedStrings["camera_quality_fhd2k"] : (this.isSupport25K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K") : LocalizedStrings["camera_quality_fhd"]);
        break;
      case 0:
      default:
        displayState = this.state.isNoneChinaLand ? MHLottieQulityToolBtnDisplayState.AUTO : this.state.isZH ? MHLottieQulityToolBtnDisplayState.AUTOCN : MHLottieQulityToolBtnDisplayState.AUTOCNTR;
        accessibilityText = LocalizedStrings["camera_quality_auto"];
        break;
    }
    if (devOpen) {
      accessibilityText = DescriptionConstants["zb_3_4"];
    }

    return (
      <MHLottieQulityToolButton
        style={ style }

        onPress={ () => {
          this.qulityToolPressed();
          TrackUtil.reportClickEvent('Camera_Definition_ClickNum');
        } }

        displayState={ displayState }
        landscape={ this.state.fullScreen }

        disabled={ (this.state.isRecording || this.state.isSleep) }
        accessibilityLabel={ accessibilityText }
        accessibilityState={ {
          disabled: this.state.isRecording || this.state.isSleep
        } }
        testID={ '111' }
      />
    );
  }

  qulityToolPressed() {
    this._hidePlayToolBarLater();
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return false;
    }
    this.setState({ dialogVisibility: true });
    return true;
  }

  _renderFullScreenToolButton(style) {

    return (
      <MHLottieFullScreenToolButton
        style={ style }

        onPress={ () => {
          this._hidePlayToolBarLater();
          if (!this.state.fullScreen) {
            this.setState({
              isWhiteVideoBackground: false,
              minX: (Host.isPad && Host.isAndroid) ? TOP_BAR_HEIGHT + 110 : TOP_BAR_HEIGHT + 60,
              minY: PREVIEW_PADDING,
            });
            this.toLandscape(2);
            this.setState({ showCameraAngleView: false });
            TrackUtil.reportClickEvent('Camera_FullScreen_ClickNum');
          } else {
            this.setState({
              isWhiteVideoBackground: true,
              minX: kWindowWidth - GESTURE_X,
              minY: TOP_BAR_HEIGHT + 60
            });
            this.toPortrait(2);
            this.setState({ showCameraAngleView: false });
          }
        } }

        displayState={ MHLottieSleepToolBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep) }
        accessibilityLabel={ DescriptionConstants.zb_4 }
        accessibilityState={
          {
            disabled: this.state.isSleep
          }
        }

      />
    );

  }

  _renderVideoFloatButton(item) {
    let itemStyle = this.state.fullScreen ? styles.videoControlBarItemLandscape : styles.videoControlBarItem;
    return (
      <View style={ itemStyle }>
        <ImageButton
          style={ {
            width: "100%", height: "100%"
          } }
          source={ item.source }
          // highlightedSource={item.highlightedSource}
          // 元素标签，获取焦点时会读出该值，默认为该元素内所有开启无障碍能力子元素的文本
          accessibilityLabel={ item.accessibilityLabel }
          onPress={ item.onPress }
          disabled={ !item.clickable }
        />
      </View>
    );
  }

  _renderBackButton() {

    if (this.state.fullScreen) {
      return null;
    }
    StatusBar.setBarStyle('light-content');
    let iconBack = require("../../Resources/Images/icon_cancle_white.png");
    let iconBackPre = require("../../Resources/Images/icon_cancle_white.png");
    let statusBarHeight = this._getStatusBarHeight();
    let containerStyle = {
      zIndex: 1,
      position: "relative",
      marginTop: statusBarHeight,
      height: navigationBarHeightFat,
      flexDirection: "row",
      alignItems: "center",
      left: 20
    };

    return (
      <LinearGradient colors={['#00000077', '#00000000']} style={{ position: 'absolute', height: (statusBarHeight + navigationBarHeightFat + 20), width: "100%", zIndex: 1 }}>
        <View style={ containerStyle }>
          <ImageButton
            style={ { width: 40, height: 40 } }
            source={ iconBack }
            highlightedSource={ iconBackPre }
            onPress={ () => {
              // if (this.state.isRecording) {
              //   Toast.success("camera_recording_block");
              //   return;
              // }
              Toast.success("user_hangup");
              this._stopAll();
              this._doGoBack();
            } }
            accessibilityLabel={ DescriptionConstants.zb_10 }
          />
        </View>
      </LinearGradient>
    );
  }

  // 这里是返回按钮
  _renderFullScreenBackButton() {

    if (!this.state.fullScreen) {
      return null;
    }

    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let containerStyle =
      {
        position: "absolute",
        left: 20,
        marginLeft: 0,
        width: 70,
        height: "100%",
        backgroundColor: "transparent",
        alignItems: "center",
        justifyContent: "center"
      };

    return (
      <View style={ containerStyle }>
        <ImageButton
          style={ { width: 40, height: 40, position: "absolute" } }
          source={ iconBack }
          highlightedSource={ iconBackPre }
          onPress={ () => {
            this._correctOrientation();
            this.toPortrait(5);
            if (Platform.OS === 'ios' && !this.isLandscapeToPortrait) {
              // 主要针对首页直播横屏进入，设备再切竖屏，直播画面不放大的问题
              this.isLandscapeToPortrait = true;
              setTimeout(() => {
                this.setState({ savedVideoScale: DEFAULT_SCALE_VALUE });
              }, 50);
            }

            if (Platform.OS === 'android' && this.props.navigation.getParam("ori") === "LANDSCAPE" && !this.isLandscapeToPortraitAndroid) {
              this.isLandscapeToPortraitAndroid = true;
              // 主要针对首页直播横屏进入，设备再切竖屏，直播画面不放大,倍速展示不正确
              setTimeout(() => {
                this.setState({ savedVideoScale: DEFAULT_SCALE_VALUE });
              }, 50);
            }

            this.setState({
              isWhiteVideoBackground: true,
              minX: kWindowWidth - GESTURE_X,
              minY: TOP_BAR_HEIGHT + 60
            });
          } }
          accessibilityLabel={ DescriptionConstants.zb_10 }
        />
      </View>
    );
  }

  _correctOrientation() {
    if (Platform.OS == "android" && this.evenLockScreen > 0) {
      this.setState({ restoreOriFinished2: false });
      setTimeout(() => {
        this.setState({ restoreOriFinished2: true });
        this._startConnect();
      }, 5);
    }
  }

  _renderLandscapeCallGroup() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }
    let positionLeft = 8;
    let screenWidth = this._getWindowPortraitHeight();

    if (Platform.OS === "ios" && screenWidth >= 800) {
      positionLeft = 85;
    }

    let landscapeButtonGroupStyle = {
      display: "flex",
      flexDirection: 'column',
      position: "absolute",
      left: positionLeft,
      bottom: 20,
      width: 80,
      height: "70%",
      marginBottom: 0,
      alignItems: "flex-end",
      justifyContent: "flex-end"
    };

    return (
      <View style={ landscapeButtonGroupStyle }>

        {/* 目前的 - Lottie动画按钮 */ }
        { this._renderSnapLandscapeButton() }
        { this._renderRecordLandscapeButton() }
        {/*{this._renderVoiceLandscapeButton()}*/ }

      </View>
    );
  }

  //全屏下面的按钮
  _renderLandscapeButtonsGroup() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }
    let positionRight = 25;
    let screenWidth = this._getWindowPortraitHeight();

    if (Platform.OS === "ios" && screenWidth >= 800) {
      positionRight = 85;
    }

    let landscapeButtonGroupStyle = {
      display: "flex",
      flexDirection: 'column',
      position: "absolute",
      right: positionRight,
      bottom: 20,
      width: 80,
      height: "70%",
      marginBottom: 0,
      alignItems: "flex-end",
      justifyContent: "flex-end"
    };

    return (
      <View style={ landscapeButtonGroupStyle }>

        {/* 目前的 - Lottie动画按钮 */ }
        { this._renderSnapLandscapeButton() }
        { this._renderRecordLandscapeButton() }
        {/*{this._renderVoiceLandscapeButton()}*/ }

      </View>
    );
  }

  _renderSnapLandscapeButton() {
    return (
      <MHLottieSnapLandscapeButton
        style={ styles.landscapeButton }

        onPress={ () => {
          this._startSnapshot();
          this._hidePlayToolBarLater();
        } }
        disabled={ (this.state.isSleep || this.state.showErrorView) }
        displayState={ MHLottieSnapLandscapeBtnDisplayState.NORMAL }
        accessibilityLabel={ !this.state.fullScreen ? DescriptionConstants.zb_6 : DescriptionConstants.zb_14 }
      />
    );
  }

  _renderRecordLandscapeButton() {
    return (
      <MHLottieRecordLandscapeButton
        style={ styles.landscapeButton }

        onPress={ () => {
          this._hidePlayToolBarLater();
          let cur = new Date().getTime();
          if (cur - this.lastTimeRecordBtnClicked < 800) {
            return;
          }
          this.lastTimeRecordBtnClicked = cur;
          if (this.state.isRecording) {
            this._stopRecord();
          } else {
            this._startRecord();
          }

        } }
        disabled={ (this.state.isSleep || this.state.showErrorView) }
        displayState={ this.state.isRecording ? MHLottieRecordLandscapeBtnDisplayState.RECORDING : MHLottieRecordLandscapeBtnDisplayState.NORMAL }
        accessibilityLabel={ !this.state.fullScreen ? DescriptionConstants.zb_7 : DescriptionConstants.zb_15 }
        accessibilityState={ {
          selected: this.state.isRecording
        } }
      />
    );
  }

  _renderVoiceLandscapeButton() {
    return (
      <MHLottieVoiceLandscapeButton
        style={ styles.landscapeButton }

        onPress={ () => {
          if (this.clickedTime && Date.now() - this.clickedTime <= 1150) {
            console.log("_renderVoiceLandscapeButton = duplicated click");
            return;
          }
          this._hidePlayToolBarLater();
          if (this.state.isCalling) {
            this._stopCall();
          } else {
            LogUtil.logOnAll("_startCall for _renderVoiceLandscapeButton press fromOneKeyCall=", this.fromOneKeyCall);
            this._startCall();
          }
        } }
        disabled={ (this.state.isSleep || this.state.showErrorView) }
        displayState={ this.state.isCalling ? MHLottieVoiceBtnDisplayState.CHATTING : MHLottieVoiceBtnDisplayState.NORMAL }
        accessible={ true }
        accessibilityState={ {
          selected: this.state.isCalling
        } }
        accessibilityLabel={ DescriptionConstants.zb_16 }
        testID={ this.state.isCalling ? '1' : '0' }
      />
    );
  }

  _renderLandscapeButton(item) {
    return (
      <View style={ styles.landscapeButton }
      >

        <ImageButton
          accessibilityLabel={ item.description }

          style={ { width: "100%", height: "100%" } }
          source={ item.source }
          // highlightedSource={item.highlightedSource}
          onPress={ item.onPress }
        />
      </View>
    );
  }

  // 这里是轮盘
  _renderLandscapeDirectionView() {
    if (!this.state.fullScreen || !this.isPtz) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }

    let landscapeButtonGroupStyle = {
      position: "absolute",
      left: 25,
      bottom: 20,
      width: 130,
      height: 130,
      alignItems: "center",
      justifyContent: "center"
    };
    return (
      <View style={ landscapeButtonGroupStyle }
      >
        <TouchableOpacity
          style={ { position: "absolute", width: "100%", height: "100%" } }
        />
        { this._renderDirectionView(false) }
      </View>
    );
  }

  _renderLandscapeCallView() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }

    let landscapeButtonGroupStyle = {
      position: "absolute",
      left: 78,
      bottom: 20,
      flexDirection: 'row'
    };
    let callTimeStr = DateFormater.instance().timeToString(this.state.callTime)
    let audioSource = this.state.audioOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_open_d.png') : require('../../Resources/Images/call_audio_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_close_d.png') : require('../../Resources/Images/call_audio_close.png');
    let videoSource = this.state.videoOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_open_d.png') : require('../../Resources/Images/call_video_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_close_d.png') : require('../../Resources/Images/call_video_close.png');
    return (
      <View style={ landscapeButtonGroupStyle } onLayout={(event) => {
        if (Host.isPad && Host.isIOS && !this.padScreenHeightHorizontal) { //iOS平板需要在横屏UI渲染完成后重新获取宽高才准确,点击全屏时延时获取不太靠谱
          // 还是会存在不准确的情况
          this.getPadInfoDelay && clearTimeout(this.getPadInfoDelay);
          this.getPadInfoDelay = setTimeout(() => {
            this.padScreenHeightHorizontal = Dimensions.get('screen').height;
            this.padScreenWidthHorizontal = Dimensions.get('screen').width;
            console.log("onLayout",this.padScreenWidthHorizontal,this.padScreenHeightHorizontal);
          },300);
        } else {
          if (Host.isPad && Host.isAndroid && !this.padScreenHeightHorizontal) {
            // 横屏时mix Flod发起通话，小窗拖动后异常 Android使用上面方法获取的宽高不对，需要使用下面的方法获取
            this.getPadHorizontalInfo && clearTimeout(this.getPadHorizontalInfo);
            this.getPadHorizontalInfo = setTimeout(() => {
              Host.getPhoneScreenInfo()
                .then((result) => {
                  this.padScreenHeightHorizontal = result.viewHeight;
                  this.padScreenWidthHorizontal = result.viewWidth;
                  console.log("pad size:",kScreenHeight,kWindowHeight,kWindowWidth,this.padScreenHeightHorizontal,this.padScreenWidthHorizontal);
                })
                .catch(() => {});
            },  400); //部分Android平板需要更长的延时才能获取到横屏后的宽高
          }
        }
      }}>
        <View style={ {
          display: 'flex',
          flexDirection: 'column',
          flexWrap: 'nowrap',
          zIndex: 1,
          alignItems: "center",
          justifyContent: "center"
        } }>

          <Text style={ { color: "#xmFFFFFF", fontSize: 14, fontWeight: "bold" } }>{callTimeStr}</Text>

          <ImageButton
            style={ { width: 65, height: 65, marginTop: 12 } }
            source={ require("../../Resources/Images/icon_call_guaduan.png") }
            onPress={ () => {
              // if (this.state.isRecording) {
              //   Toast.success("camera_recording_block");
              //   return;
              // }
              if (this.state.isCallPrepare) {
                Toast.success("device_is_prepare_call");
                return;
              }
              Toast.success("user_hangup");
              if (this.props.navigation.getParam("ori") !== "LANDSCAPE") {
                console.log("竖屏执行");
                this.toPortrait(5);
                // 全屏点击挂断，返回首页后UI异常
                this.delayToGoBack && clearTimeout(this.delayToGoBack);
                this.delayToGoBack = setTimeout(() => {
                  this._stopAll();
                  this._doGoBack();
                },800);
              } else {
                this._stopAll();
                this._doGoBack();
              }

            } }
          />
        </View>
        <View style={ {
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'nowrap',
          zIndex: 1,
          alignItems: "flex-end",
          justifyContent: "center",
          marginTop: 12,
          marginBottom: 7
        } }>

          <ImageButton
            style={ { width: 50, height: 50, marginLeft: 36 } }
            source={ this.state.audioOpen ? require('../../Resources/Images/call_audio_open.png') : require('../../Resources/Images/call_audio_close.png') }
            onPress={ () => {
              this._controlAudio();
            } }
          />
          <ImageButton
            style={ { width: 50, height: 50, marginLeft: 25 } }
            source={ this.state.videoOpen ? require('../../Resources/Images/call_video_open.png') : require('../../Resources/Images/call_video_close.png') }
            onPress={ () => {
              this._controlCamera();
            } }
          />

          <ImageButton
            style={ { width: 50, height: 50, marginLeft: 25 } }
            source={ require("../../Resources/Images/call_audio_volume_write_bg.png") }
            onPress={ () => {
              this._controlVolume();
            } }
          />
          <ImageButton
            disabled={!this.state.videoOpen}
            style={ { width: 50, height: 50, marginLeft: 25 } }
            source={ this.state.videoOpen ? require("../../Resources/Images/call_change_camera_write_bg.png") : require("../../Resources/Images/call_change_camera_close.png") }
            onPress={ () => {
              console.log("++++++++++++++++++++++change camera");
              handlerOnceTap(() => {
                console.log("++++++++++++++++++++++change camera real");
                this.setState({ cameraType: this.state.cameraType === 0 ? 1 : 0 });
              },800);
          }}
          />
        </View>
      </View>
    );
  }

  _renderCloudVipRenewView() {
    if (this.state.fullScreen) {
      return null;
    }

    if (!this.state.showCloudVipBuyTip) {
      return null;
    }

    let cloundRenewVipStyle = {
      display: "flex",
      flexDirection: "row",
      position: "relative",
      width: "100%",
      height: 30,
      backgroundColor: "#32BAC0FF"
    };

    let renewVipTextStyle = {
      width: "80%",
      left: 20,
      color: "#ffffff",
      fontSize: kIsCN ? 12 : 10,
      textAlignVertical: 'center'
    };

    let tipStr = this.isCloudServer ? LocalizedStrings["eu_c_cloudvip_end_tip"] : LocalizedStrings["c_cloudvip_end_tip"];
    if (this.isVip && this.cloudVipWillEndDays > 0) {
      if (this.cloudVipWillEndDays == 1) {
        tipStr = (this.isCloudServer ? LocalizedStrings["eu_c_cloudvip_will_end_tip_one"].replace("%1$d", this.cloudVipWillEndDays) : LocalizedStrings["c_cloudvip_will_end_tip_one"]).replace("%1$d", this.cloudVipWillEndDays);
      } else {
        tipStr = (this.isCloudServer ? LocalizedStrings["eu_c_cloudvip_will_end_tip_few"].replace("%1$d", this.cloudVipWillEndDays) : LocalizedStrings["c_cloudvip_will_end_tip_few"]).replace("%1$d", this.cloudVipWillEndDays);
        if (Host.locale.language === "pl" && this.isCloudServer) {
          if ((this.cloudVipWillEndDays >= 2 && this.cloudVipWillEndDays <= 19) || this.cloudVipWillEndDays >= 22 && this.cloudVipWillEndDays <= 24) {
            tipStr = LocalizedStrings["eu_c_cloudvip_will_end_tip_other"].replace("%1$d", this.cloudVipWillEndDays);
          }
        }
        if (Host.locale.language === "ru" && this.isCloudServer) {
          if (this.cloudVipWillEndDays == 0 || (this.cloudVipWillEndDays >= 5 && this.cloudVipWillEndDays <= 19)) {
            tipStr = LocalizedStrings["eu_c_cloudvip_will_end_tip_other"].replace("%1$d", this.cloudVipWillEndDays);
          }
        }
      }
    }
    return (
      <View style={ cloundRenewVipStyle }>

        <TouchableOpacity
          activeOpacity={ 1.0 }
          style={ { display: "flex", width: "100%", height: "100%", alignContent: "center", justifyContent: "center" } }
          onPress={ () => {
            console.log("why!, clound vip renew");
            LogUtil.logOnAll(TAG, "点击了续费按钮，跳到云存储列表");
            this._showCloundStorage();
          } }>
          {
            <Text style={ renewVipTextStyle }>
              { tipStr }
            </Text>
          }
          <TouchableOpacity
            style={ {
              position: "absolute",
              right: 0,
              width: 50,
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            } }
            activeOpacity={ 0.5 }
            onPress={ () => {
              StorageKeys.HIDE_CLOUD_BUY_TIP = true;
              this.setState({ showCloudVipBuyTip: false });
            } }
          >
            <Image style={ { width: 20, height: 20 } }
                   source={ require('../../Resources/Images/icon_cloud_close_nor.png') }
            >
            </Image>
          </TouchableOpacity>

        </TouchableOpacity>

      </View>
    );
  }

  _renderControlLayout() {
    if (this.state.fullScreen) {
      return null;
    }
    let controlLayout = {
      // position: "relative",
      display: "flex",
      zIndex: 1,
      backgroundColor: "#F6F6F6",
      bottom: 0,
      width: "100%",
      flexDirection: "column",
      flexWrap: 'nowrap',
      flexGrow: 1
    };

    if (this.state.darkMode) {
      controlLayout.backgroundColor = "#ffffff";
    }

    return (
      <View style={ controlLayout } pointerEvents="box-none">

        { Platform.OS == "ios" ? this._renderCallControlView() : this._renderCallControlViewAndroid() }
        {/*{this._renderFixControlView()}*/ }
        {/* 这里是下面说话截图录屏的 */ }

        {/*<View style={{ position: "relative", width: "100%", flex: 1, zIndex: 0 }}>*/ }
        {/*  /!* 这里看家 *!/*/ }

        {/*  {this._renderOptionsView()}*/ }
        {/*  {this._renderOptionsCoverView()}*/ }
        {/*</View>*/ }
      </View>
    );
  }

  _renderCallControlViewAndroid() {
    if (this.state.fullScreen) {
      return null;
    }

    let fixContolViewStyle = {
      display: "flex",
      flexDirection: "row"
    };

    // if (this.state.darkMode) {
    //   fixContolViewStyle.backgroundColor = "#EEEEEE";
    // }
    // 18+52+12+13+12+70+14+14+28 = 233
    // 18+52+12+13+12+70+14+14
    let callTimeStr = DateFormater.instance().timeToString(this.state.callTime);
    let audioSource = this.state.audioOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_open_d.png') : require('../../Resources/Images/call_audio_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_close_d.png') : require('../../Resources/Images/call_audio_close.png');
    let videoSource = this.state.videoOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_open_d.png') : require('../../Resources/Images/call_video_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_close_d.png') : require('../../Resources/Images/call_video_close.png');
    return (
      <View style={ { marginTop: this.opTopMargin ? this.opTopMargin : 18 } }>
        <View style={ fixContolViewStyle }
        >
          <View style={ { width: '50%' } }>
            <ImageTextButton
              style={{marginHorizontal: 30 }}
              imageStyle={ { width: 60, height: 60 } }
              textStyle={ { fontSize: Util.isLanguageCN() ? 13 : 11, marginTop: 12, color: "rgba(0,0,0,0.6)" } }
              text={ this.state.audioOpen ? LocalizedStrings['call_audio_open_tx'] : LocalizedStrings['call_audio_close_tx'] }
              onPress={ () => {
                this._controlAudio();
              } }
              source={ audioSource }
              highlightedSource={ audioSource }/>
          </View>
          <View style={ { width: '50%' } }>
            <ImageTextButton
              style={{marginHorizontal: 30 }}
              imageStyle={ { width: 60, height: 60 } }
              textStyle={ { fontSize: Util.isLanguageCN() ? 13 : 11, marginTop: 12, color: "rgba(0,0,0,0.6)" } }
              text={ this.state.videoOpen ? LocalizedStrings['call_camera_open_tx'] : LocalizedStrings['call_camera_close_tx'] }
              onPress={ () => {
                this._controlCamera();
              } }
              source={ videoSource }
              highlightedSource={ videoSource }/>
          </View>

        </View>
        <View style={ { flexDirection: 'row', flexWrap: 'nowrap',   marginTop: this.opMiddleMargin ? this.opMiddleMargin : 12 } }>
          <View style={{width:'50%',alignItems: 'center'}}>

            <ImageButton
              style={ { width: 60, height: 60, marginTop: 6 } }
              source={ Util.isDark() ? require('../../Resources/Images/icon_call_voice_dark_mode.png') : require('../../Resources/Images/icon_call_voice.png') }
              onPress={ () => {
                this._controlVolume();
              } }
            >

            </ImageButton>
          </View>

          <View style={ {
            position: "absolute",
            display: 'flex',
            flexDirection: 'column',
            flexWrap: 'nowrap',
            alignItems: "center",
            justifyContent: "center",
            left:0,
            right:0
          } }>
            <ImageButton
              style={ { width: 70, height: 70 } }
              source={ require("../../Resources/Images/icon_call_guaduan.png") }
              onPress={ () => {
                // if (this.state.isRecording) {
                //   Toast.success("camera_recording_block");
                //   return;
                // }
                if (this.state.isCallPrepare) {
                  Toast.success("device_is_prepare_call");
                  return;
                }
                Toast.success("user_hangup");
                this._stopAll();
                this._doGoBack();
              } }
            />

            <Text style={ {
              color: "black",
              fontSize: 13,
              marginTop: 14,
              fontWeight: "bold",
              alignSelf: "center"
            } }>{callTimeStr}</Text>

          </View>
          <View style={{width:'50%',alignItems: 'center'}}>

            <ImageButton
              disabled={!this.state.videoOpen}
              style={ { width: 60, height: 60, marginTop: 6 } }
              source={ this.state.videoOpen ? Util.isDark() ? require('../../Resources/Images/icon_call_camera_change_dark_mode.png') : require('../../Resources/Images/icon_call_camera_change.png') :
                Util.isDark() ? require('../../Resources/Images/icon_call_camera_change_dis_dark_mode.png') : require('../../Resources/Images/icon_call_camera_change_dis.png') }
              onPress={ () => {
                console.log("++++++++++++++++++++++change camera");
                handlerOnceTap(() => {
                  console.log("++++++++++++++++++++++change camera real");
                  this.setState({ cameraType: this.state.cameraType === 0 ? 1 : 0 });
                },800);
              } }
            >

            </ImageButton>
          </View>
        </View>

      </View>

    );
  }

  _controlAudio() {
    if ((new Date().getTime() - this.startAudioTime) < 1000) {
      // 点击的太频繁
      return;
    }
    console.log("_controlAudio",this.state.audioOpen);
    this.startAudioTime = new Date().getTime();
    let cmd = MISSCommand_ECO.MISS_CMD_MIC_OPEN;
    if (this.state.audioOpen) {
      // 音频处于开启状态
      this.cameraGLView && this.cameraGLView.stopAudioRecord();
      cmd = MISSCommand_ECO.MISS_CMD_MIC_CLOSE;
    } else {
      this.cameraGLView && this.cameraGLView.startAudioRecord();
      cmd = MISSCommand_ECO.MISS_CMD_MIC_OPEN;
    }
    Service.miotcamera.sendP2PCommandToDevice(cmd, {}).then((res) => console.log("CMD MIC SUCCESS")).catch((err) => console.log("CMD MIC ERROR", err));
    this.setState({ audioOpen: !this.state.audioOpen });
  }

  // 视频摄像机的开启、关闭
  _controlCamera() {
    if ((new Date().getTime() - this.startvideoTime) < 1000) {
      // 点击的太频繁
      return;
    }
    this.startvideoTime = new Date().getTime();

    if (this.state.videoOpen) {
      this.setState({ videoOpen: false });
      this._stopVideoCommand();
    } else {
      // 1、需要先检查权限 2、下发指令 3最后是开启关闭摄像头预览
      // 视频通话 需要请求camera权限
      this.requestVideoCall();
    }
  }

  _controlVolume() {
    if (Device.isReadonlyShared) {
      Toast.show(LocalizedStrings.cloud_share_hint);
      return;
    }

    this.volumeNewValue = this.state.callVolumeValue;
    this.setState({ showChangeVolumeDialog: true, tempCallVolumeValue: this.state.callVolumeValue });
  }

  _renderCallControlView() {
    if (this.state.fullScreen) {
      return null;
    }

    let fixContolViewStyle = {
      display: "flex",
      flexDirection: "row"
    };

    // if (this.state.darkMode) {
    //   fixContolViewStyle.backgroundColor = "#EEEEEE";
    // }
    // 18+52+12+13+12+70+14+14+28 = 233
    let callTimeStr = DateFormater.instance().timeToString(this.state.callTime);
    let audioSource = this.state.audioOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_open_d.png') : require('../../Resources/Images/call_audio_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_audio_close_d.png') : require('../../Resources/Images/call_audio_close.png');
    let videoSource = this.state.videoOpen ? DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_open_d.png') : require('../../Resources/Images/call_video_open.png') :
      DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/call_video_close_d.png') : require('../../Resources/Images/call_video_close.png');
    return (
      <View style={ { marginTop: this.opTopMargin ? this.opTopMargin : 18 } } pointerEvents="box-none">
        <View style={ fixContolViewStyle }
        >
          <View style={ { width: '50%' } }>
            <ImageTextButton
              style={{marginHorizontal: 30 }}
              imageStyle={ { width: 60, height: 60 } }
              textStyle={ { fontSize: 13, marginTop: 12, color: "rgba(0,0,0,0.6)" } }
              text={ this.state.audioOpen ? LocalizedStrings['call_audio_open_tx'] : LocalizedStrings['call_audio_close_tx'] }
              onPress={ () => {
                console.log("麦克风点击")
                this._controlAudio();
              } }
              source={ audioSource }
              highlightedSource={ audioSource }/>
          </View>
          <View style={ { width: '50%' } }>
            <ImageTextButton
              style={{marginHorizontal: 30 }}
              imageStyle={ { width: 60, height: 60 } }
              textStyle={ { fontSize: 13, marginTop: 12, color: "rgba(0,0,0,0.6)" } }
              text={ this.state.videoOpen ? LocalizedStrings['call_camera_open_tx'] : LocalizedStrings['call_camera_close_tx'] }
              onPress={ () => {
                console.log("摄像头点击");
                this._controlCamera();
              } }
              source={ videoSource }
              highlightedSource={ videoSource }/>
          </View>

        </View>
        <View style={ { flexDirection: 'row', marginTop: this.opMiddleMargin ? this.opMiddleMargin : 12 } } >

          <View style={ {
            position: "absolute",
            display: 'flex',
            flexDirection: 'column',
            flexWrap: 'nowrap',
            alignItems: "center",
            justifyContent: "center",
            left: 0,
            right: 0
          } }>
            <ImageButton
              style={ { width: 70, height: 70 } }
              source={ require("../../Resources/Images/icon_call_guaduan.png") }
              onPress={ () => {
                // this._stopCall();
                // if (this.state.isRecording) {
                //   Toast.success("camera_recording_block");
                //   return;
                // }
                if (this.state.isCallPrepare) {
                  Toast.success("device_is_prepare_call");
                  return;
                }
                Toast.success("user_hangup");
                this._stopAll();
                this._doGoBack();
              } }
            />
            <Text style={ {
              color: "black",
              fontSize: 13,
              marginTop: 14,
              fontWeight: "bold",
              alignSelf: "center"
            } }>{callTimeStr}</Text>
          </View>
          <View style={{position: "absolute", left: 0, width:'50%',alignItems: 'center'}} pointerEvents="box-none">
            <ImageButton
              style={ { width: 60, height: 60, marginTop: 6 } }
              source={ Util.isDark() ? require('../../Resources/Images/icon_call_voice_dark_mode.png') : require('../../Resources/Images/icon_call_voice.png') }
              onPress={ () => {
                this._controlVolume();
              } }
            >

            </ImageButton>
          </View>
          <View style={{position: "absolute", right: 0, width:'50%',alignItems: 'center'}} pointerEvents="box-none">
            <ImageButton
              disabled={!this.state.videoOpen}
              style={ { width: 60, height: 60, marginTop: 6 } }
              source={ this.state.videoOpen ? Util.isDark() ? require('../../Resources/Images/icon_call_camera_change_dark_mode.png') : require('../../Resources/Images/icon_call_camera_change.png') :
                Util.isDark() ? require('../../Resources/Images/icon_call_camera_change_dis_dark_mode.png') : require('../../Resources/Images/icon_call_camera_change_dis.png') }
              onPress={ () => {
                // this.setState({offsetY:this.state.offsetY+0.1})
                console.log("摄像头翻转");
                console.log("++++++++++++++++++++++change camera");
                handlerOnceTap(() => {
                  console.log("++++++++++++++++++++++change camera real");
                  this.setState({ cameraType: this.state.cameraType === 0 ? 1 : 0 });
                },800);
              } }
            >

            </ImageButton>
          </View>
        </View>

      </View>


    );
  }

  _renderFixControlView() {
    if (this.state.fullScreen) {
      return null;
    }

    let fixContolViewStyle = {
      display: "flex",
      zIndex: 2,
      marginBottom: 30,
      height: fixControlBarHeight,
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      paddingLeft: this.isPtz ? 25 : 0,
      paddingRight: this.isPtz ? 25 : 0,
      justifyContent: this.isPtz ? "space-between" : "center",
      backgroundColor: "#ffffff",
      borderBottomLeftRadius: 30,
      borderBottomRightRadius: 30
    };

    if (this.state.darkMode) {
      fixContolViewStyle.backgroundColor = "#EEEEEE";
    }

    let buttonStyle = this.isPtz ? { position: "relative", height: iconSize, width: iconSize } : {
      position: "relative",
      height: iconSize,
      width: iconSize,
      marginLeft: 34,
      marginRight: 34
    };

    return (
      <View style={ fixContolViewStyle }
      >
        { this.isPtz ? <View style={ {
          position: "absolute",
          bottom: 0,
          left: 0,
          width: "100%",
          height: "100%",
          backgroundColor: "transparent",
          borderBottomLeftRadius: 20,
          borderBottomRightRadius: 20
        } }
                             { ...this.panResponder.panHandlers }
        /> : null
        }

        {/* 目前的 - Lottie动画按钮 */ }
        { this._renderVoiceButton(buttonStyle) }
        { this._renderSnapButton(buttonStyle) }
        { this._renderRecordButton(buttonStyle) }
        { this.isPtz ? this._renderControlButton(buttonStyle) : null }

      </View>

    );
  }

  _toggleSwitch() {
    if (this.state.showPoweroffView) {
      Toast.fail(this.getPowerOffString());
      return;
    }

    // if (this.isReadonlyShared) {
    //   Toast.fail('cloud_share_hint');
    //   return;
    // }

    let preShowStatus = this.state.showPanoView;
    this.setState({ showPanoView: !this.state.showPanoView });

    TrackUtil.reportClickEvent(preShowStatus ? 'Camera_PanoramaClose_ClickNum' : 'Camera_Panorama_ClickNum');

    if (preShowStatus) {
      return;
    }
    if (this.isAllViewRpc && this.state.panoViewStatus == 2) {
      return;
    }
    this.setState({ panoViewStatus: 0 }); //成功之后置为0

    if (!this.panoramaImgStoreUrl) {
      let timeStamp = new Date().getTime(); // 整型，用于做唯一性标识
      this.panoTimestamp = timeStamp & 0x7fffffff;
      let params = { timeStamp: this.panoTimestamp, panoramaType: this.panoramaType };
      this.startPanoIconAnimation();
      setTimeout(() => {
        this._getMergePhotoMeta(params, -1);
      });
    } else {
      this.setState({ panoViewStatus: 3 });
    }
  }

  _renderPanoViewButtons() {

    // 这里panoParmam现时undefined 后来添加了了this.panoParam.isSupport 全景绘图海外依旧是没有的。。。
    if (!this.panoParam || !this.panoParam.isSupport || this.state.isInternationServer) {
      return;
    }

    const switchIcons = [
      {
        source: !this.state.darkMode ? require('../../Resources/Images/icon_switch_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_switch_pano_confirm_nor_dark.png'),
        highlightedSource: !this.state.darkMode ? require('../../Resources/Images/icon_switch_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_switch_pano_confirm_nor_dark.png'),
        accessibilityLabel: DescriptionConstants.zb_21,
        onPress: () => this._toggleSwitch()// 切换
      },
      {
        source: !this.state.darkMode ? require('../../Resources/Images/icon_switch_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_switch_pano_confirm_nor_dark.png'),
        highlightedSource: !this.state.darkMode ? require('../../Resources/Images/icon_switch_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_switch_pano_confirm_nor_dark.png'),
        accessibilityLabel: DescriptionConstants.zb_21,
        onPress: () => this._toggleSwitch()// 切回去
      }
    ];
    const editIcons = [
      {
        source: !this.state.darkMode ? this.prePositions.length == 0 ? require('../../Resources/Images/icon_edit_pano_dis.png') : require('../../Resources/Images/icon_edit_pano_nor.png')
          : require('../../Resources/Images/icon_edit_pano_nor_dark.png'),
        highlightedSource: !this.state.darkMode ? require('../../Resources/Images/icon_edit_pano_nor.png')
          : require('../../Resources/Images/icon_edit_pano_nor_dark.png'),
        accessibilityLabel: DescriptionConstants.zb_20,
        onPress: () => {
          if (VersionUtil.Model_chuangmi_051a01 != Device.model) {
            this.setState({ editPonoView: true });
          }
          this.props.navigation.navigate("EditSelectPrePositions", {
            positionArray: this.prePositions,
            callBack: (params) => {
              console.log("call back data=", params);
              AlarmUtil.putPrePositions(params).then(() => {
                this._getPreSetPosition();
              }).catch((err) => {
                console.log("call back data to set err=", err);
              });
            }
          });
        }
      },
      {
        source: !this.state.darkMode ? require('../../Resources/Images/icon_edit_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_edit_pano_confirm_nor_dark.png'),
        highlightedSource: !this.state.darkMode ? require('../../Resources/Images/icon_edit_pano_confirm_nor.png')
          : require('../../Resources/Images/icon_edit_pano_confirm_nor_dark.png'),
        accessibilityLabel: DescriptionConstants.zb_20,
        onPress: () => {
          this.setState({ editPonoView: false });
        }
      }
    ];

    let switchIndex = this.state.showPanoView ? 0 : 1;
    let editIndex = this.state.editPonoView ? 1 : 0;
    // let editIcon = this.state.editPonoView ? require('../../Resources/Images/icon_confirm.png') : require('../../Resources/Images/icon_edit_black.png');
    // 这里是切换
    let switchBtnStyle = {
      width: 40,
      height: 40

    };

    let editBtnStyle = {
      width: 40,
      height: 40,
      marginRight: 20
    };

    // if (this.state.darkMode) {
    //   switchBtnStyle.tintColor = "#C0C0C0FF"; // IMG_DARKMODE_TINT;
    //   editBtnStyle.tintColor = IMG_DARKMODE_TINT;
    // }

    return (
      <View style={ {
        position: "absolute",
        height: 40,
        top: 3,
        right: 26,
        zIndex: 10,
        display: "flex",
        flexDirection: "row"
      } }>


        {
          this.state.showPanoView && (this.state.panoViewStatus == 3 || VersionUtil.Model_chuangmi_051a01 == Device.model) ?

            <ImageButton style={ editBtnStyle }
                         source={ editIcons[editIndex].source }
                         onPress={ editIcons[editIndex].onPress }
                         accessibilityLabel={ editIcons[editIndex].accessibilityLabel }
                         disabled={ this.prePositions.length == 0 }
            /> : null }

        <ImageButton style={ switchBtnStyle }
                     source={ switchIcons[switchIndex].source }
                     onPress={ switchIcons[switchIndex].onPress }
                     accessibilityLabel={ switchIcons[switchIndex].accessibilityLabel }
        />

      </View>

    );
  }

  _renderDirectionLayout() {
    let height = this._getDirectionViewHeight();
    let directTop = this._getDirectionViewTop();
    let directionStyle = {
      position: "relative",
      backgroundColor: DarkMode.getColorScheme() == "dark" ? '#EEEEEE' : '#ffffff',
      marginTop: directTop,
      height: height,
      width: height,
      alignItems: "center",
      justifyContent: "center"
    };
    return (
      <View style={ directionStyle }>
        { this._renderDirectionView(true) }
      </View>
    );
  }

  _renderDirectionView(isPortrait) {
    if (this.isHorizontalPTZ) {
      return this._renderHorizontalDirectionView(isPortrait);
    }
    return (
      <DirectionView
        isPortrait={ isPortrait }
        accessibilityLabel={ DescriptionConstants.zb_12 }
        ref={ (ref) => {
          this.directionView = ref;
        } }
        onActionDown={ () => {
          if (this.showPlayToolBarTimer) {
            clearTimeout(this.showPlayToolBarTimer);
            this.showPlayToolBarTimer = null;
          }
          if (this.state.isSleep) {
            Toast.fail(this.getPowerOffString());
            return;
          }
          if (this.isReadonlyShared) {
            Toast.fail("cloud_share_hint");
            return;
          }
          console.log('onActionDown 按下得时候 走进这里');

        } }
        onActionUp={ (off) => {
          if (this.isReadonlyShared) {
            return;
          }
          if (off) {
            this._sendDirectionCmd(DirectionViewConstant.CMD_OFF);
          }
          this._hidePlayToolBarLater();
          console.log('onActionUp 抬起得时候 走进这里');
        } }

        onClickDirection={ (type) => {
          if (this.isReadonlyShared) {
            return;
          }
          this._sendDirectionCmd(type);
          TrackUtil.reportClickEvent("Camera_direction_ClickNum"); // Camera_direction_ClickNum
        } }
      />
    );
  }

  _renderHorizontalDirectionView(isPortrait) {
    return (
      <DirectionHorizontalView
        accessibilityLabel={ DescriptionConstants.zb_19 }
        isPortrait={ isPortrait }
        ref={ (ref) => {
          this.directionView = ref;
        } }
        onActionDown={ () => {
          if (this.state.isSleep) {
            Toast.fail(this.getPowerOffString());
          }
          if (this.isReadonlyShared) {
            Toast.fail("cloud_share_hint");
            return;
          }
        } }
        onActionUp={ (off) => {
          if (this.isReadonlyShared) {
            return;
          }
          if (off) {
            this._sendDirectionCmd(DirectionViewConstant.CMD_OFF);
          }
        } }

        onClickDirection={ (type) => {

          if (this.isReadonlyShared) {
            return;
          }
          this._sendDirectionCmd(type);
          TrackUtil.reportClickEvent("Camera_direction_ClickNum"); // Camera_direction_ClickNum
        } }
      />
    );
  }

  _sendDirectionCmd(type) {
    if (this.state.isSleep) {
      return;
    }
    if (type == DirectionViewConstant.DIRECTION_lEFT || type == DirectionViewConstant.DIRECTION_RIGHT ||
      type == DirectionViewConstant.DIRECTION_UP || type == DirectionViewConstant.DIRECTION_BOTTOM) {
      clearTimeout(this.angleViewTimeout);
      this.setState({ showCameraAngleView: true, angleViewShowScale: false });
      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
    }

    if ((type == DirectionViewConstant.DIRECTION_lEFT && this.state.angle == MAX_ANGLE) ||
      (type == DirectionViewConstant.DIRECTION_RIGHT && this.state.angle == MIN_ANGLE) ||
      (type == DirectionViewConstant.DIRECTION_UP && this.state.elevation == MAX_ELEVATION) ||
      (type == DirectionViewConstant.DIRECTION_BOTTOM && this.state.elevation == MIN_ELEVATION)) {

    }
    CameraPlayer.getInstance().sendDirectionCmd(type);
  }

  startPanoIconAnimation() {
    if (this.panoAnimationLoading) {
      return;
    }
    this.iconAnimatedValue.setValue(0);
    this.panoAnimationLoading = Animated.timing(
      this.iconAnimatedValue,
      {
        duration: 1200,
        toValue: 360,
        easing: Easing.linear
      }
    ).start(() => {
      if (this.state.panoViewStatus == 2 || this.state.panoViewStatus == 0) {
        this.startPanoIconAnimation();
      } else {
        this.panoAnimationLoading = null;
      }
    });
  }

  _getPreSetPosition() {
    console.log("start get_preset_position");
    AlarmUtil.getPrePositions().then((res) => {
      this.addPreSetIndex = 1;
      console.log(`get_preset_position = ${ JSON.stringify(res) }`);
      this.prePositions = JSON.parse(res[0].value);
      this._updatePreSetPositions();
    }).catch((err) => {
      this.addPreSetIndex = 1;
      this.prePositions = [];
      this.prePositionItems = [{ "pre_pos_stu": -9999 }];
      console.log(`get_preset_position error = ${ err }`);
      this.forceUpdate();
    });

  }

  _updatePreSetPositions() {
    this.prePositionItems = [];
    this.prePositions.sort(function(a, b) {
      return a.idx - b.idx;
    });
    this.prePositions.forEach((item) => {
      if (this.addPreSetIndex == item.idx) {
        this.addPreSetIndex = item.idx + 1;
      }
    });
    this.prePositions.sort(function(a, b) {
      return a.location - b.location;
    });
    this.preSetPositionExist = false;
    this.prePositions.forEach((item) => {
      this.preSetPositionExist = true;
      this.prePositionItems.push(item);
      if (this.addPreSetLocation == item.location) {
        this.addPreSetLocation = item.location + 1;
      }
    });
    if (this.prePositionItems.length < 6) {
      this.prePositionItems.push({ "pre_pos_stu": -9999 });
    }
    // console.log(`get_preset_position prePositions = ${ JSON.stringify(this.prePositions) }`);
    this.forceUpdate();
  }

  _ctrlPreSetPosition(type, idx, pos) {
    if (type == this.PreSetCTRL) {
      this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
      this.ctrlCurrentLocation[0] = idx;
      let posXY = JSON.parse(pos);
      this.ctrlCurrentLocation[1] = posXY[0];
      this.ctrlCurrentLocation[2] = posXY[1];
      console.log(`ctrlCurrentLocation = ${ JSON.stringify(this.ctrlCurrentLocation) }`);
      this._sendDirectionCmd(DirectionViewConstant.CMD_GET);
    }
    AlarmUtil.activePrePositions(idx).then((res) => {
      console.log(`activePrePositions res=${ JSON.stringify(res[0]) }`);
      if (res.code == 0) {
        if (res.result == 0) {
          let imgPath = `${ this.preSetPositionImg }${ idx }.jpg`;
          switch (type) {
            case this.PreSetCTRL:
              if (this.ctrlCurrentLocation[0] == idx) {
                this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
                this.delayUpdatePreSetImg = setTimeout(() => {
                  this._updatePreSetPositionImg(imgPath, idx);
                }, 2000);
              }
              break;
            case this.PreSetADD: {
              this._updatePreSetPositionImg(imgPath, idx);
              break;
            }
            case this.PreSetDELETE: {
              Host.file.deleteFile(`${ imgPath }`);
              this.preSetPositionImgTime[idx] = Date.now();
              break;
            }
          }
          // this._getPreSetPosition();
        } else {
          console.log(`set_preset_position res=${ JSON.stringify(res.error) }`);
          Toast._showToast(`${ type } error = ${ JSON.stringify(res.error.code) }`, true);
        }
      }
    }).catch((err) => {
      console.log(`set_preset_position res=${ JSON.stringify(err) }`);
    });
  }

  _updatePreSetPositionImg(imgPath, idx) {
    AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip, `${ imgPath }`).then(() => {
      this.preSetPositionImgTime[idx] = Date.now();
    }).catch(() => {
      this.preSetPositionImgTime[idx] = Date.now();
    });
  }

  isTextcommon(str) {
    let arrList = this.prePositionItems;
    return arrList.some((res) => {
      return res.name === Base64.encode(str);
    });
  }

  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
        (codePoint == 0x9) ||
        (codePoint == 0xA) ||
        (codePoint == 0xD) ||
        ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
        ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
        ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }

  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      } else if (str.match(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/)) {
        return true;
      }
      // else if(this.isTextcommon(str)){
      //   return true
      // }
    }
    return false;
  }



  _getPanorama() {
    console.log("_getPanorama111");
    LogUtil.logOnAll(TAG, "点击全景图绘制，开始绘制了");
    if (this.isAllViewRpc) {
      Toast.fail("pano_view_title_drawing");
      return;
    }
    this.isAllViewRpc = true;

    let method = "get_panoram";

    let timeStamp = new Date().getTime(); // 整型，用于做唯一性标识
    this.panoTimestamp = timeStamp & 0x7fffffff;

    let params = { timeStamp: this.panoTimestamp, panoramType: this.panoramaType };
    console.log("why! get_panoram params=", params);

    this.getPanoViewRetries = 0;
    // 发送Rpc
    RPC.callMethod(method, params)
      .then((res) => {
        LogUtil.logOnAll(TAG, "全景图绘制RPC成功，结果：" + JSON.stringify(res));
        console.log("why! _getPanorama, res:", res);
        let panoramState = res.panoramState;
        if (panoramState == 1) {
          Toast.success("panorama_ing");
          this.setState({ showPanoView: false, panoViewStatus: 0 });
          return;
        }
        this._clearTimer(this.mRpcCallTimer);
        this.mRpcCallTimer = setTimeout(() => {
          LogUtil.logOnAll(TAG, "开始从服务器请求全景图数据");
          const intervalID = setInterval(() => {
            let params = {
              timeStamp: this.panoTimestamp
            };
            console.log(`get panoview interval, retry: ${ this.getPanoViewRetries }`);
            // 获取全景图信息
            this.getPanoViewRetries += 1;
            if (this.getPanoViewRetries <= 30) {
              let paramInteralId = this.panoramaImgStoreUrl ? intervalID : -1;
              this._getMergePhotoMeta(params, paramInteralId);
              // this._getMergePhotoMeta(params, intervalID);

            } else {
              LogUtil.logOnAll(TAG, "连续30次查询全景图都失败了，886");
              this.isAllViewRpc = false;
              clearInterval(intervalID);
              this.setState({ panoViewStatus: 1 });
            }

          }, 5000);
          if (this.state.isSleep) {
            clearInterval(intervalID);
          }
        });
      })
      .catch((err) => {
        this.isAllViewRpc = false;
        LogUtil.logOnAll(TAG, "全景图绘制RPC失败：" + JSON.stringify(err));
        console.log("get_panoram err=", err);
      });
  }

  _getMergePhotoMeta(params, intervalID) {
    // 获取全景图信息
    // this._getPanoramPhotoMeta(params);

    console.log(`_getPanoramaPhotoMeta, intervalID: ${ intervalID }`);

    API.get("/common/app/get/mergePhotoMeta", "business.smartcamera", params)
      .then((result) => {
        console.log(`why!, _getPanoramaPhotoMeta result=${ JSON.stringify(result) }`);

        // {"result":"ok","retriable":false,"code":0,
        // "data":{"borderRx":94,"modifyTime":2147483647,"stoId":"GLACho5r7ejIv3zr5wWDAdirUihhK9YZz780pOg3uwQW8W7Cbc79EvIZZKY9o2Djma0Y8Gkl4K6P5KO49U_hjLbbmxILDen2T2yuz5zV0of67fUv512SnZOtME3I11razpvvycPRaM-DJb4iGEtib3WeFy2BlzDvJAfihNexPyrc3lO1ahAyi82Bd9kSd4BaYIyOuWmv6LaVequWb0xF1qYcg_ZfopCsWh4_qhH8KDVNed2KJRJ9EJlBYs8n6ZK6p6MB1QYbnktt_pFPBl4K73BBBFhLi1ztj4iZwTOnHXwQxEgk6tJEf5kkCbxBQ5-dt4lmyUekz_BzmNCYxTJloTrzQj_GtpA9Dpq3ZCYWwKE3Jxk1plnVbk_ZB1VDZPJG99FGC6Ig-Uic50WTeaOvwZxJGBgSL7kUkNH7RmK1QMs15XOVDg4BGBDTYnXzyhUJyCObW8h36D9wGBSrcK2ENggawp9WRtbGGicWOgpBYQA",
        // "borderRy":51,"borderLx":10,"borderLy":51,"userId":1115522811,"did":"324203916"},
        // "description":"成功","ts":1591599641024}

        let hasValidResult = false;
        if (result.data && result.data.stoId) {
          let modifyTime = result.data.modifyTime;
          let stoId = result.data.stoId;
          let borderLx = result.data.borderLx;
          let borderRx = result.data.borderRx;
          let borderLy = result.data.borderLy;
          let borderRy = result.data.borderRy;

          console.log(`modifyTime: ${ modifyTime }, this.panoTimestamp: ${ this.panoTimestamp }`);
          if (modifyTime == this.panoTimestamp) {
            console.log("timestamp match");
          } else {
            console.log("timestamp not match");
          }

          if ((modifyTime == this.panoTimestamp || intervalID == -1) && stoId && stoId.length > 0 && borderLx && borderRx && borderLy && borderRy) {
            clearInterval(intervalID);
            let did = Device.deviceID;
            let imgId = stoId;
            let hostParams = { prefix: "processor.smartcamera.", method: "GET", path: "/miot/camera/app/v1/img" };
            let pathParams = { did: did, fileId: "0", stoId: stoId };

            console.log(`getCommonImgWithParams imgId=${ imgId }`);
            console.log(`getCommonImgWithParams JSON.stringify(hostParams)=${ JSON.stringify(hostParams) }`);
            console.log(`getCommonImgWithParams JSON.stringify(pathParams)=${ JSON.stringify(pathParams) }`);
            console.log(`getCommonImgWithParams did=${ did }`);

            console.log(`borderLx = ${ borderLx }`);
            console.log(`borderRx = ${ borderRx }`);

            LogUtil.logOnAll(TAG, "全景图请求" + this.getPanoViewRetries + "次  请求成功:" + JSON.stringify(result.data));

            hasValidResult = true;
            Service.miotcamera.getCommonImgWithParams(imgId, JSON.stringify(hostParams), JSON.stringify(pathParams), did)
              .then((result) => {

                console.log(`getCommonImgWithParams result: ${ result }`);
                this.allViewImageStoId = stoId;
                this.isAllViewRpc = false;
                console.log(`getCommonImgWithParams result=${ JSON.stringify(result) }`);
                this.minSelectPositionLeft = borderLx;
                this.maxSelectPositionRight = borderRx;
                this.panoramaImgStoreUrl = result;

                this.showPanoAfterReceivedRotateAngle = true;
                let getRotationTime = intervalID > 0 ? 10000 : 100;
                this.showPanoToastAfterReceivedRotateAngle = intervalID > 0 ? true : false;
                this._clearTimer(this.mGetRotationTimer);
                this.mGetRotationTimer = setTimeout(() => {
                  this._getRotateAngle();
                }, getRotationTime);

                let fileExist = Host.file.isFileExists(result);
                if (fileExist) {
                  LogUtil.logOnAll(TAG, "全景图下载成功，路径：" + result);

                  console.log("save panorama");
                  StorageKeys.PANORAMA_IMAGE_PATH = this.panoramaImgStoreUrl;
                  let panoParams = {
                    type: this.panoramaType,
                    minLeft: this.minSelectPositionLeft,
                    maxRight: this.maxSelectPositionRight
                  };
                  StorageKeys.PANORAMA_PARAM = JSON.stringify(panoParams);
                } else {
                  this.setState({ panoViewStatus: 1 });
                }
              })
              .catch((err) => {
                this.isAllViewRpc = false;
                this.setState({ panoViewStatus: 1 });
                console.log(`getCommonImgWithParams err=${ JSON.stringify(err) }`);
              });
          }
        }
        // else {
        //   if (!this.isAllViewRpc)
        //     this.setState({ panoViewStatus: 1 });
        // }
        if (!hasValidResult && !this.isAllViewRpc) {
          console("why!, not has valid result");
          this.setState({ panoViewStatus: 1 });
        }

      })
      .catch((err) => {
        console.log(`_getPanoramaPhotoMeta err=${ JSON.stringify(err) }`);
        this.isAllViewRpc = false;
        this.setState({ panoViewStatus: 1 });
        clearInterval(intervalID);
      });
  }

  _renderVoiceButton(style) {

    return (
      <MHLottieVoiceButton
        ref={ (ref) => {
          this.mHLottieVoiceButton = ref;
        } }
        style={ style }
        label="rnlabelCall"
        description="rndescriptionCall"
        onPress={ () => {
          console.log(TAG, "startSpeakerTime:" + this.startSpeakerTime);
          if ((new Date().getTime() - this.startSpeakerTime) < 1000) {
            // Toast.fail("click_too_fast");
            return;
          }
          this.startSpeakerTime = new Date().getTime();
          if (this.state.isCalling) {
            // 停止isAudioCallInCommunication 只有才挂断
            this.setState({isAudioCallInCommunication:false},()=>{
              this._stopCall();
            })
          } else {
            LogUtil.logOnAll("_startCall for _renderVoiceButton onPress fromOneKeyCall=", this.fromOneKeyCall);
            this._startCall();
          }
        } }

        displayState={ this.state.isCalling ? MHLottieVoiceBtnDisplayState.CHATTING : MHLottieVoiceBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep || this.state.showErrorView) }

        darkMode={ this.state.darkMode }

        accessible={ true }
        accessibilityState={ {
          selected: this.state.isCalling,
          disabled: this.state.isSleep || this.state.showErrorView
        } }
        accessibilityLabel={ !this.state.fullScreen ? DescriptionConstants.zb_5 : DescriptionConstants.zb_16 }
        testID={ this.state.isCalling ? '1' : '0' }
      />
    );
  }

  _renderSnapButton(style) {

    return (
      <MHLottieSnapButton
        style={ style }
        accessibilityState={ {
          disabled: this.state.isSleep || this.state.showErrorView
        } }
        accessibilityLabel={ !this.state.fullScreen ? DescriptionConstants.zb_6 : DescriptionConstants.zb_14 }
        label="rnlabelSnap"
        description="rndescriptionSnap"

        onPress={ () => {
          this.setState({ screenshotVisiblity: false, screenshotPath: null }, () => {
            this._startSnapshot();
          });
        } }

        displayState={ MHLottieSnapBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep || this.state.showErrorView) }

        darkMode={ this.state.darkMode }
      />
    );
  }

  _renderRecordButton(style) {

    return (
      <MHLottieRecordButton
        style={ style }
        label="rnlabelRecord"
        description="rndescriptionRecord"
        onPress={ () => {
          if (this.state.isRecording) {
            this._stopRecord();
          } else {
            this._startRecord();
          }

        } }

        displayState={ this.state.isRecording ? MHLottieRecordBtnDisplayState.RECORDING : MHLottieRecordBtnDisplayState.NORMAL }

        disabled={ (this.state.isSleep || this.state.showErrorView) }

        darkMode={ this.state.darkMode }
        accessible={ true }
        accessibilityState={ {
          selected: this.state.isRecording,
          disabled: this.state.isSleep || this.state.showErrorView
        } }
        accessibilityLabel={ DescriptionConstants.zb_7 }
        testID={ this.state.isRecording ? '1' : '0' }
      />
    );
  }

  _renderControlButton(style) {
    return (
      <MHLottieControlButton
        style={ style }
        accessibilityLabel={ DescriptionConstants.zb_8 }
        label="rnlabelControl"
        description="rndescriptionControl"
        accessibilityState={ {
          selected: this.state.showDirectCtr
        } }
        onPress={ () => {
          if (this.state.showDirectCtr) {
            this._showDirectionViewAnimated(false);
          } else {
            this._showDirectionViewAnimated(true);
          }
        } }

        displayState={ this.state.showDirectCtr ? MHLottieControlBtnDisplayState.CONTROLLING : MHLottieControlBtnDisplayState.NORMAL }

        darkMode={ this.state.darkMode }
      />
    );
  }


  _renderVideoFixButton(item) {

    let buttonStyle = this.isPtz ? { position: "relative", height: iconSize, width: iconSize } : {
      position: "relative",
      height: iconSize,
      width: iconSize,
      marginLeft: 34,
      marginRight: 34
    };
    // let colorScheme = DarkMode.getColorScheme();
    // if (colorScheme == 'dark') {
    //   buttonStyle.tintColor = IMG_DARKMODE_TINT;
    // }
    return (
      <ImageButton style={ buttonStyle }
                   source={ item.source }
                   highlightedSource={ item.highlightedSource }
                   accessibilityLabel={ item.accessibilityLabel }
                   onPress={ item.onPress }
                   disabled={ !item.clickable }
      />
    );
  }

  _renderLongPressButton(item) {
    let buttonStyle = this.isPtz ? { position: "relative", height: iconSize, width: iconSize } : {
      position: "relative",
      height: iconSize,
      width: iconSize,
      marginLeft: 34,
      marginRight: 34
    };
    return (
      <View>
        <Image style={ buttonStyle }
               source={ item.source }
               highlightedSource={ item.highlightedSource }
        />
        <TouchableOpacity
          style={ {
            position: "absolute",
            flexDirection: "row",
            flexGrow: 1,
            width: "100%",
            height: 50,
            display: "flex",
            alignItems: "center"
          } }
          onLongPress={ () => {
            console.log("enter longpress");
            clearTimeout(this.longPressTimer);
            this.longPressTimer = setTimeout(() => {
              LogUtil.logOnAll("_startCall for _renderLongPressButton onLongPress fromOneKeyCall=", this.fromOneKeyCall);
              this._startCall();
            }, 500);
          } }
          onPressOut={ () => {
            clearTimeout(this.longPressTimer);
            this._stopCall();
          } }
        >
        </TouchableOpacity>
      </View>
    );
  }

  _showDirectionViewAnimated(isShow) {
    this.setState({ showDirectCtr: isShow });
    let ctrViewToHeight = isShow ? fixControlBarHeight + this._getDirectionContainerHeight() : fixControlBarHeight;
    let duration = Math.abs(ctrViewToHeight - this._controlViewHeight) * 300 / this._getDirectionContainerHeight();

    let toAlpha = isShow ? 0.5 : 0;

    Animated.timing(
      this.state.controlViewHeight,
      {
        toValue: ctrViewToHeight,
        duration: duration
      }
    ).start();

    Animated.timing(
      this.state.optionCoverAlpha,
      {
        toValue: toAlpha,
        duration: duration
      }
    ).start();
    TrackUtil.reportClickEvent('Camera_ShowHidedirection_ClickNum');
    TrackUtil.reportResultEvent('Camera_direction_Status', 'type', isShow ? 1 : 2);
  }

  _showCloundStorage() { // VIP 进入原生云存插件， 非vip, 10068后直接进入购买云存页面
    TrackUtil.reportClickEvent("Camera_CloudStorage_ClickNum"); // Camera_CloudStorage_ClickNum
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return;
    }
    if (!Device.isOwner && !this.isVip) {
      Toast.success("share_user_permission_hint");
      return;
    }
    if (this.isVip) {
      Service.miotcamera.showCloudStorageSetting();
    } else {
      API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: !this.isVip ? "P2P_snackbar_expired" : "P2P_snackbar_cancel" }) : Service.miotcamera.showCloudStorageSetting();
      CameraConfig.isToUpdateVipStatue = true;
    }
  }

  _showAllStorage() {
    TrackUtil.reportClickEvent("Camera_Storage_ClickNum"); // Camera_Storage_ClickNum
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return;
    }
    // if (!Device.isOwner && !this.isVip) {
    //   Toast.success("cloud_share_hint");
    //   return;
    // }
    let index = 0;
    if (this.isVip) {
      index = 0;
    } else if (this.sdcardCode == 0 || this.sdcardCode == 2 || !Device.isOwner) {
      index = 2;
    }

    if (!CameraConfig.isSupportCloud() || !this.mFirmwareSupportCloud) {
      index = index - 1;
      index = index > -1 ? index : 0;
    }
    let navigationParam = {
      initPageIndex: index,
      vip: this.isVip,
      isSupportCloud: this.mFirmwareSupportCloud && CameraConfig.isSupportCloud()
    };
    LogUtil.logOnAll("AllStorage UI s param:", navigationParam, " isConnected:", CameraPlayer.getInstance().isConnected());
    // 进入回看前 清空一次SdFileManager里的列表。避免缓存的问题
    SdcardEventLoader.getInstance().clearSdcardFileList();
    this.props.navigation.navigate("AllStorage", navigationParam);
  }

  _showOperationBannerPage() {
    TrackUtil.reportClickEvent("Camera_Recommend_ClickNum");// 推荐运营位点击
    this._showNativeWebPage(this.bannerItem.h5Url);
    let shortKey = this.state.bannerShortKey;
    this.setState({ clickedBannerShortKey: shortKey });
    StorageKeys.OPERATION_CLICKED_KEY = shortKey;
  }

  _showNativeWebPage(h5) {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return;
    }

    let data = { h5Url: h5, sdcardGetSuccess: true, sdcardStatus: this.sdcardCode };

    let showSdcard = this.showSdcardPage;
    if (this.sdcardCode == 3) {
      showSdcard = false;
    }

    let newData = {
      url: h5,
      sdcardCode: this.sdcardCode,
      isVip: this.isVip,
      isShowSdcardPage: showSdcard,
      showFace: Device.isOwner
    };
    // TODO: replaced by miotcamera api since 10053
    // Service.miotcamera.showOperationBannerPage(data);
    // NativeModules.MHCameraSDK.showOperationBannerPage(Device.deviceID, JSON.stringify(data));
    clearTimeout(this.snapshotTimer);

    this.props.navigation.navigate("NativeWebPage", newData);
    let shortKey = this.state.bannerShortKey;
    this.setState({ clickedBannerShortKey: shortKey });
    StorageKeys.OPERATION_CLICKED_KEY = shortKey;
  }

  // 授权看家
  _renderOptionsCoverView() {
    if (!this.state.showDirectCtr) {
      return null;
    }

    let containerStyle = {
      position: "absolute",
      height: "100%",
      width: "100%",
      backgroundColor: "#000000",
      opacity: this.state.optionCoverAlpha
    };
    return (
      <Animated.View style={ containerStyle }>
      </Animated.View>
    );
  }

  _startCall(focus = false) {

    LogUtil.logOnAll(`_startCall for focus=${ focus } fromOneKeyCall=`, this.fromOneKeyCall);

    TrackUtil.reportClickEvent("Camera_VoiceCall_ClickNum"); // Camera_VoiceCall_ClickNum
    if (this.state.isSleep) {
      Toast.success(this.getPowerOffString());
      return;
    }

    LogUtil.logOnAll(`-=-=-=-=-=showPauseView=${ this.state.showPanoView } showErrorView=${ this.state.showErrorView } pstate=${ this.state.pstate } focus=${ focus }`);
    if ((this.state.showPauseView || this.state.showErrorView || this.state.pstate < 2) && !focus) {
      Toast.success("call_no_play");
      return;
    }
    console.log(TAG, "startCall: isClickCall " + this.isClickCall + " this.state.isCalling:" + this.state.isCalling);
    if (this.isClickCall) {
      LogUtil.logOnAll("this.isClickCall = true");
      return;
    }
    this.clickedTime = Date.now();
    this.isClickCall = true;

    if (this.state.isCalling) {
      LogUtil.logOnAll("this.state.isCalling = true");
      return;
    }

    this.enterCallTime = new Date().getTime();
    this.setState({ showPlayToolBar: true });
    if (this.isNetworkToReconnect) {
      this.callType = this.state.videoOpen ? 0 : 1;
      this._startAudioCommand();
      this.isNetworkToReconnect = false;
    } else {
      this.startCallByCallType();
    }

  }

  /**
   * @Author: byh
   * @Date: 2024/3/8
   * @explanation:
   * 无论什么情况进来，speaker都开启指令都下发下去
   *********************************************************/
  startCallByCallType() {
    console.log("startCallByCallType",this.callType);
    this.setState({ audioOpen: this.callType == 0 || this.callType == 1, videoOpen: this.callType == 0 || this.callType == 2 },() => {
      this.callCMDSend();
    });

  }

  callCMDSend() {
    if (this.callType == 0 || this.callType == 2) {
      // 音视频通话 or 视频，未打开麦克风
      this.requestAudioVideoCall();
    } else if (this.callType == 1 || this.callType == 3) {
      // 语音通话 or mic\摄像头都关闭
      this.requestAudioCall();
    }
  }

  /**
   * 开启视频通话
   */
  requestAudioVideoCall() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      let talkPreRecordPermission = new Date().getTime();
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, null)
        .then((granted) => {
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, null)
              .then((granted1) => {
                if (granted1 === PermissionsAndroid.RESULTS.GRANTED) {
                  this.isCheckingPermission = false;
                  console.log("granted", granted);
                  this.isClickCall = false;
                  // if ((new Date().getTime() - talkPreRecordPermission) > 5000) { // android 授权弹窗会导致收不到onPressOut,这里主动停止
                  //   console.log("========time is long",new Date().getTime() - talkPreRecordPermission);
                  //   this.isClickCall = false;
                  //   return;
                  // }
                  // 请求权限时
                  // this.setState({ videoOpen : false }, () => {
                  //   this.setState({ videoOpen : true })
                  // });
                  // 开启对讲
                  this._startAudioCommand();
                  // 开启视频预览通道
                  // this._startVideoCommand();
                  // setTimeout(()=>{
                  //   this._startVideoCommand();
                  // },500);
                } else if (granted1 === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                  // Toast.success("camera_no_audio_permission");
                  this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
                  this.isClickCall = false;
                  // 摄像头有权限
                  this._startVideoCommand();
                } else {
                  Toast.success("camera_no_audio_permission");
                }
                this.isClickCall = false;
              }).catch((error) => {
                console.log('android--StartRecord-error-1-', error);
                Toast.show(LocalizedStrings["action_failed"], {duration: Toast.durations.LONG});
            });
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_audio_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 2 });
            this.isClickCall = false;
          } else {
            Toast.success("auth_fail");
          }

    }).catch((error) => {
      this.isCheckingPermission = false;
      Toast.fail("action_failed", error);
      this.isClickCall = false;
    });


    } else {
      System.permission.request(Permissions.CAMERA).then((res) => {
        System.permission.request(Permissions.RECORD_AUDIO).then((res) => {
          // 开启对讲
          this._startAudioCommand();

          // this._startVideoCommand();
          this.isClickCall = false;
        }).catch((error) => {
          this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
          this._startVideoCommand();
          this.isClickCall = false;
        })
      }).catch((error) => {
        // 这里提示没有权限
        // Toast.success("camera_no_audio_permission");
        this.isClickCall = false;
        this.setState({ showPermissionDialog: true, permissionRequestState: 2 });
      });
    }
  }
  /**
   * 开启对讲
   * 1、请求麦克风录制权限
   * 2、发送开启对讲命令
   */
  requestAudioCall() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      let talkPreRecordPermission = new Date().getTime();
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          this.isClickCall = false;
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            // if ((new Date().getTime() - talkPreRecordPermission) > 5000) { // android 授权弹窗会导致收不到onPressOut,这里主动停止
            //   this.isClickCall = false;
            //   return;
            // }
            this._startAudioCommand();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_audio_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
            this.isClickCall = false;
          } else {
            Toast.success("camera_no_audio_permission");
          }
          this.isClickCall = false;
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.fail("action_failed", error);
        this.isClickCall = false;
      });
    } else {
      System.permission.request(Permissions.RECORD_AUDIO).then((res) => {
        this._startAudioCommand();
        this.isClickCall = false;
      }).catch((error) => {
        // 这里提示没有权限
        // Toast.success("camera_no_audio_permission");
        this.isClickCall = false;
        this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
      });
    }
  }

  requestVideoCall() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      let talkPreRecordPermission = new Date().getTime();
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          this.isClickCall = false;
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            // if ((new Date().getTime() - talkPreRecordPermission) > 1500) { // android 授权弹窗会导致收不到onPressOut,这里主动停止
            //   this.isClickCall = false;
            //   return;
            // }
            this._startVideoCommand();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_audio_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 2 });
            this.isClickCall = false;
          } else {
            Toast.success("auth_fail");
          }
          this.isClickCall = false;
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.fail("action_failed", error);
        this.isClickCall = false;
      });
    } else {
      System.permission.request(Permissions.CAMERA).then((res) => {
        this._startVideoCommand();
        this.isClickCall = false;
      }).catch((error) => {
        // 这里提示没有权限
        // Toast.success("camera_no_audio_permission");
        this.isClickCall = false;
        this.setState({ showPermissionDialog: true, permissionRequestState: 2 });
      });
    }
  }

  _startVideoCommand() {
    // Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEOCALL_START_REQ, {})
    LogUtil.logOnAll("video call user info:",Service.account.nickName,Service.account.avatarURL);
    if (!Service.account.nickName) {
      Service.account.load().then((res) => {
        this._realStartVideoCommand();
      }).catch((error) => {
        this._realStartVideoCommand(false);
      });
    } else {
      this._realStartVideoCommand();
    }
  }

  _realStartVideoCommand(isInfoOk = true) {
    Service.miotcamera.sendP2PCommandToDevice(0x118, {uid: Service.account.ID, name: isInfoOk ? Service.account.nickName : "", icon: isInfoOk ? Service.account.avatarURL : ""})
      .then((retCode) => {
        console.log("video on get send callback");
        console.log(retCode);
      })
      .catch((err) => {
        Toast.fail("action_failed", err);
      });
  }

  _stopVideoCommand(justSendCommand = false) {
    console.log("stopVideoCommand",MISSCommand.MISS_CMD_VIDEOCALL_STOP,0x11a);
    // Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEOCALL_STOP, {})
    Service.miotcamera.sendP2PCommandToDevice(0x11a, {})
      .then((retCode) => {
        console.log("video on get send stop callback");
        console.log(retCode);
        if (!justSendCommand) {
          this.setState({ videoOpen: false },()=> {
            if (Platform.OS == 'ios') {
              this.cameraCaptureView && NativeModules.MHCameraCaptureViewManager.stopCameraCapture(findNodeHandle(this.cameraCaptureView));
            } else {
              this.cameraCaptureView && UIManager.dispatchViewManagerCommand(
                findNodeHandle(this.cameraCaptureView),
                "stopCameraCapture",
                [],
              );
            }
          });
        }
      })
      .catch((err) => {
        if (!justSendCommand) {
          Toast.fail("action_failed", err);
        }
      });
  }

  _startAudioCommand() {
    LogUtil.logOnAll("audio call user info:",Service.account.nickName,Service.account.avatarURL);
    if (!Service.account.nickName) {
      Service.account.load().then((res) => {
        this._realStartAudioCommand();
      }).catch((err) => {
        LogUtil.logOnAll("Service.account.load error",err);
        this._realStartAudioCommand(false);
      });
    } else {
      this._realStartAudioCommand();
    }

  }

  _realStartAudioCommand(isInfoOk = true) {
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_START_REQ, { uid: Service.account.ID, name: isInfoOk ? Service.account.nickName : "", icon: isInfoOk ? Service.account.avatarURL : "" })
      .then((retCode) => {
        LogUtil.logOnAll("audio on get send callback",retCode);
        if (this.firstSendCallCMD) {
          // 发出指令后，由于p2p连接断开，导致指令被丢弃，无法正常开启通话，这里再次发送相关指令一次
          this.firstSendCallCMD = false;
          this.startCallAgainTimeout && clearTimeout(this.startCallAgainTimeout);
          this.startCallAgainTimeout = setTimeout(() => {
            LogUtil.logOnAll(TAG,"CDM Send again");
            this.callCMDSend();
          },2500);
        }
      })
      .catch((err) => {
        Toast.fail("action_failed", err);
      });
  }
  _stopAudioCommand() {
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_STOP, {})
      .then((retCode) => {
        console.log("audio stop on get send callback");
        console.log(retCode);
        this.setState({ audioOpen: false });
      })
      .catch((err) => {
        Toast.fail("action_failed", err);
      });
  }

  _stopCall() {
    console.log("stop call: thisstate.iscalling:" + this.state.isCalling + " cameraGlView:" + (this.cameraGLView != null) + " destroyed:" + this.destroyed);
    // if (!this.state.isCalling) {
    //   return;
    // }
    if (this.callIsStop) {
      LogUtil.logOnAll(TAG,"Call CMD stop is do");
      return;
    }
    this.callIsStop = true;
    LogUtil.logOnAll(TAG,"Call CMD is to do");
    if (this.enterCallTime > 0) {
      let callTime = (new Date().getTime() - this.enterCallTime) / 1000;
      TrackUtil.reportResultEvent("Camera_VoiceCall_Time", "Time", callTime); // Camera_VoiceCall_Time
      this.enterCallTime = 0;
    }
    clearTimeout(this.callTimeout);
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_STOP, {}).then((retCode) => {
      LogUtil.logOnAll(TAG,"MISS_CMD_SPEAKER_STOP SUCCESS");
      this.fromOneKeyCall = false;
      this.startCallFlag = false;
    }).catch((err) => {
      LogUtil.logOnAll(TAG,"MISS_CMD_SPEAKER_STOP error",err);
    });
    if (this.cameraGLView != null && !this.destroyed) {
      this.cameraGLView.stopAudioRecord();
    }

    this._stopVideoCommand(true);

    this._disableAudioBtnTemporarily();
    this._toggleAudio(this.isAudioMuteTmp);// 恢复对讲之前的状态

    this.setState({
      isCalling: false
    });
    console.log("this.iscalling = false");
    this.isClickCall = false;
  }

  _disableAudioBtnTemporarily() {
    if (Platform.OS == "android") {
      return;
    }
    this.setState({ isAudioBtnDisabled: true });
    this._clearTimer(this.mSetAudioBtnStateTimer);
    this.mSetAudioBtnStateTimer = setTimeout(() => {
      this.setState({ isAudioBtnDisabled: false });
    }, 500);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false, true);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.success("action_failed");
      });
    } else {
      // no ios's photos const use hardcode
      this.iosPermissionRequest = true;
      System.permission.request("photos").then((res) => {
        this.iosPermissionRequest = false;
        this._realStartSnapshot(false, true);
      }).catch((error) => {
        this.iosPermissionRequest = false;
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
    TrackUtil.reportClickEvent('Camera_ScreenShot_ClickNum');
  }

  _realStartSnapshot(isFromVideo, shouldShowPop) {
    AlbumHelper.snapShot(this.cameraGLView)
      .then((path) => {
        console.log(path, 'path');
        this.isForVideoSnapshot = isFromVideo;
        if (!shouldShowPop) {
          return;// 点右上角的设置时，调用截图api，不弹框
        }
        clearTimeout(this.snapshotTimeout);
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 5000);// v1 比 v3 需要更多时间
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        Toast.success("action_failed");
      });
  }

  _stopRecord() {
    if (!this.state.isRecording) {
      return;
    }
    if (this.stopRecordingTime && new Date().getTime() - this.stopRecordingTime < 1000) {
      console.log("============stop is do====================")
      return;
    }
    this.stopRecordingTime = new Date().getTime();
    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": this.state.resolution })
        .then(() => { // 不修改这些信息。

        })
        .catch((err) => {
          console.log(err);
        });
    }

    if (this.state.isMute) { // 原本静音
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then(() => {

      });
      if (this.cameraGLView != null && !this.destroyed) {
        this.cameraGLView.stopAudioPlay();
      }
    }
    if (this.cameraGLView == null || this.destroyed) {
      this.setState({ isRecording: false, recordTimeSeconds: 0 });
      return;
    }
    this.cameraGLView.stopRecord().then(() => {
      console.log("stopRecord success branch");
      this.setState({ isRecording: false, recordTimeSeconds: 0 });
      if (this.videoRecordPath == null || this.videoRecordPath == "") {
        LogUtil.logOnAll("stopRecord failed this.videoRecordPath=", this.videoRecordPath);
        return;
      }
      this.mRecordOkTimer = setTimeout(() => { // setTimneout锁屏后不再执行，开屏后自动执行
        // 录制成功后 要把视频转存储到相册。
        AlbumHelper.saveToAlbum(this.videoRecordPath, true)
          .then((result) => {
            this.setState({
              videoName: result,
              isRecording: false,
              recordTimeSeconds: 0
            });
            if (this.justSnapshotResult) {
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: AlbumHelper.getSnapshotName() });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            } else { // 截图失败的时候，就使用videoRecordPath
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: this.videoRecordPath });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            }
            console.log(result);
          })
          .catch((err) => {
            console.log(err);
          });
      });
    })
      .catch((error) => {
        LogUtil.logOnAll(TAG, "stopRecord failed reason:", JSON.stringify(error));
        this.setState({ isRecording: false, recordTimeSeconds: 0 });
        if (error == -2) {
          Toast.fail("record_video_failed_time_mini");
        } else {
          Toast.fail("record_video_failed");
        }
      });

    this.justSnapshotResult = false;
    AlbumHelper.justSnapshot(this.cameraGLView)
      .then((path) => {
        this.justSnapshotResult = true;
      })
      .catch((error) => {
        this.justSnapshotResult = false;
      });
  }

  _startRecord() {
    if (this.state.showLoadingView) {
      Toast.success("action_failed");
      return;
    }
    if (Platform.OS === "ios") {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this.realStartRecord();
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    } else {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.realStartRecord();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        // Toast.fail("action_failed", error);
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });

      });
    }
    TrackUtil.reportClickEvent('Camera_Record_ClickNum');
  }

  realStartRecord() {
    // 切换清晰度为高清
    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": 3 })
        .then(() => { // 不修改这些信息。
          // this.setState({ resolution: index })
          // StorageKeys.LIVE_VIDEO_RESOLUTION = index;
          this._realStartRecord(1);
        })
        .catch((err) => {
          console.log(err);
          this._realStartRecord(2);
        });
    } else {
      this._realStartRecord(3);
    }
  }

  _realStartRecord(fromi) {
    console.log(`_realStartRecord called from: ${ fromi }`);
    // 打开声音
    if (this.state.isMute) { // 不是有声音 开启声音通道 不播放
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio start get send callback");
        console.log(retCode);
      });
    }

    let path = AlbumHelper.getDownloadTargetPathName(true);
    this.videoRecordPath = path;
    if (this.cameraGLView == null || this.destroyed) {
      return;
    }
    this.cameraGLView.startRecord(`${ Host.file.storageBasePath }/${ path }`, kRecordTimeCallbackName)
      .then((retCode) => {
        console.log(`start record, retCode: ${ retCode }`);
        this.setState({ isRecording: true, screenshotVisiblity: false });
      })
      .catch((err) => {
        console.log(err);
        Toast.success("action_failed");
      });
  }

  _changeResolution(position) {

    // if (this.state.pstate < 2) {//没有连接上
    //   //
    //   return;
    // }
    // if (this.state.isRecording) {
    //   //
    //   return;
    // }

    let index = 0;
    switch (position) {
      case 1:
        index = 1;
        break;
      case 2:
        if (VersionUtil.Model_chuangmi_051a01 == Device.model
          || CameraConfig.Model_chuangmi_069a01 == Device.model) {
          index = 2;
        } else {
          index = 3;
        }
        break;
      case 3:
        index = 3;
        break;
      default:
        index = 0;
        break;
    }

    // show dialog for user to choose
    // send p2p cmds
    this.sendResolutionCmd(index);
    TrackUtil.reportResultEvent('Camera_Definition_Status', 'type', position + 1);
  }

  sendResolutionCmd(index, ignoreState = false) {
    Service.miotcamera.sendP2PCommandToDevice(
      MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": index })
      .then(() => {
        if (!ignoreState) {
          this.setState({ resolution: index });
        }
        StorageKeys.LIVE_VIDEO_RESOLUTION = index;
      })
      .catch((err) => {
        console.log(err);
      });
  }

  _toggleSleep(isSleep) {
    this.isAllViewRpc = false;

    if (this.isReadonlyShared) {
      Toast.fail('cloud_share_hint');
      return;
    }
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return;
    }

    if (this.isClickSleep) {
      return;
    }

    if (isSleep) {
      this.forceSleep = true;
    } else {
      this.forceSleep = false;
    }
    this.isClickSleep = true;

    this.lastClickSleepTime = new Date().getTime();
    SpecUtil.toggleSleep(isSleep)
      .then(() => {
        this._powerOffHandler(!isSleep, true, true);
      })
      .catch((err) => {
        this.isClickSleep = false;
        if (VersionUtil.judgeIsV1(Device.model)) { // 不管。。。v1原生插件这么干的

        } else {
          Toast.fail("action_failed");
        }
      });

  }

  _toggleAudio(isMute, changeUnitMute = true) {
    if (this.state.isMute == isMute) { // 状态一致，没有必要走到下面。
      return;
    }
    if (this.cameraGLView == null || this.destroyed) {
      return;
    }
    console.log(TAG, "isMute:" + isMute + " changeUnitMUte:" + changeUnitMute);
    if (isMute) {
      if (this.state.isMute) { // 已经静音
        return;
      }
      if (!this.state.isRecording) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop get send callback");
          console.log(retCode);
        });
      }
      console.log(TAG, "stopAudioPlay called");
      this.cameraGLView.stopAudioPlay();
      this.setState({ isMute: true });
      // if (changeUnitMute) {
      //   CameraConfig.setUnitMute(true);
      // }
      return;
    }
    if (!this.state.isRecording) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio start get send callback");
        console.log(retCode);
      });
    }
    this.cameraGLView.startAudioPlay();
    this.setState({ isMute: false });
    // CameraConfig.setUnitMute(false);

  }

  _renderResolutionDialog() {
    // if (Platform.OS == "ios") {
    return this._renderResolutionDialog_ios(); // 不再区分平台
    // } else {
    //   let index = 0;
    //   switch (this.state.resolution) {
    //     case 1:
    //       index = 1;
    //       break;
    //     case 2:
    //       index = 2;
    //       break;
    //     case 3:
    //       if (VersionUtil.Model_chuangmi_051a01 == Device.model) {
    //         index = 3;
    //       } else {
    //         index = 2;
    //       }
    //       break;
    //     default:
    //       index = 0;
    //       break;
    //   }
    //   let fhdname = this.isSupport2K ? LocalizedStrings["camera_quality_fhd2k"] : (this.isSupport25K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K") 
    //     : this.isSupport3K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "3K") : LocalizedStrings["camera_quality_fhd"]);
    //   let midname = VersionUtil.Model_chuangmi_051a01 == Device.model ? LocalizedStrings["camera_quality_fhd"].replace("1080", "720") : fhdname;
    //   let lowName = CameraConfig.isSupport480P(Device.model) ? LocalizedStrings["camera_quality_low"].replace("360", "480") : LocalizedStrings["camera_quality_low"];
    //   let dataSourceArr = VersionUtil.Model_chuangmi_051a01 == Device.model ?
    //     [LocalizedStrings["camera_quality_auto"], lowName, midname, fhdname]
    //     : [LocalizedStrings["camera_quality_auto"], lowName, fhdname];

    //   return (
    //     <SingleChoseDialog title={LocalizedStrings["camera_quality_choose"]}
    //       dataSource={dataSourceArr}
    //       timeout={0}
    //       check={index}
    //       onCheck={(e) => {
    //         console.log(this.state.resolution, 'this.state.resolution')
    //         console.log('onCheck', e);
    //         let position = e.position;
    //         this._changeResolution(position);
    //         this.setState({ dialogVisibility: false });
    //       }}
    //       onDismiss={() => {
    //         console.log('onDismiss');
    //         this.setState({ dialogVisibility: false });
    //       }}
    //       visible={this.state.dialogVisibility}
    //       accessibilityLabel={DescriptionConstants.zb_46}
    //     />
    //   );
    // }
  }

  _renderResolutionDialog_ios() {
    let fhdname = CameraConfig.Model_chuangmi_069a01 == Device.model ? LocalizedStrings["camera_quality_super_fhd"]
      : this.isSupport2K ? LocalizedStrings["camera_quality_fhd2k"] : (this.isSupport25K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K")
        : this.isSupport3K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "3K") : LocalizedStrings["camera_quality_fhd"]);
    let midname = CameraConfig.Model_chuangmi_069a01 == Device.model ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "").replace(" ", "").replace("高清", "高清HD")
      : VersionUtil.Model_chuangmi_051a01 == Device.model ? LocalizedStrings["camera_quality_fhd"].replace("1080", "720") : fhdname;
    let lowName = CameraConfig.Model_chuangmi_069a01 == Device.model ? LocalizedStrings["camera_quality_sd"]
      : CameraConfig.isSupport480P(Device.model) ? LocalizedStrings["camera_quality_low"].replace("360", "480") : LocalizedStrings["camera_quality_low"];
    let dataSourceArr = CameraConfig.Model_chuangmi_069a01 == Device.model ? [{ title: LocalizedStrings["camera_quality_auto"] }, { title: lowName }, { title: midname }, { title: fhdname }]
      : VersionUtil.Model_chuangmi_051a01 == Device.model ?
        [{ title: LocalizedStrings["camera_quality_auto"] }, { title: lowName }, { title: midname }, { title: fhdname }]
        : [{ title: LocalizedStrings["camera_quality_auto"] }, { title: lowName }, { title: fhdname }];
    return (
      <ChoiceDialog
        // modalStyle={ {
        //   marginBottom: -16,
        //   marginLeft: this.state.fullScreen ? 78 : 0,
        //   width: this.state.fullScreen ? (this._getWindowPortraitHeight() - 78 * 2) : "100%"
        // } }
        modalStyle={{
          width: this.state.fullScreen ? 310 : "100%",
          height: this.state.fullScreen ? 280 : null,
          marginHorizontal: this.state.fullScreen ? (kWindowHeight - 310) / 2 : null,
          marginVertical: this.state.fullScreen ? (kWindowWidth - 280) / 2 : null,
          marginBottom: !this.state.fullScreen ? -16 : null,
          borderBottomLeftRadius: this.state.fullScreen || Host.isPad ? 20 : 0,
          borderBottomRightRadius: this.state.fullScreen || Host.isPad ? 20 : 0
        }}
        visible={ this.state.dialogVisibility }
        title={ LocalizedStrings["camera_quality_choose"] }
        options={ dataSourceArr }
        selectedIndexArray={ this.selectedIndexArray }
        itemStyleType={ 2 }
        onDismiss={ (_) => this.setState({ dialogVisibility: false }) }
        onSelect={ (result) => {
          this.selectedIndexArray = result;
          this._changeResolution(result[0]);
          this.setState({ dialogVisibility: false });
        } }
        accessibilityLabel={ DescriptionConstants.zb_46 }
      />
    );
  }


  _putSD_STATUS_EJECTED() {
    AlarmUtil.putSD_STATUS_EJECTED(false).then((res) => {
      LogUtil.logOnAll(`putSD_STATUS_EJECTED res=${ JSON.stringify(res) }`);
      this.putedSD_STATUS_EJECTED = 1;
    }).catch((err) => {
      LogUtil.logOnAll(`putSD_STATUS_EJECTED err=${ JSON.stringify(err) }`);
      this.putedSD_STATUS_EJECTED = -1;
    });
  }

  async onePopUp(data, state) { //存储到本地，仅出现一次弹窗方法，data:本地存储对象名；state:弹窗状态名
    let res = await StorageKeys[data];
    // const res = false;
    LogUtil.logOnAll("onePopUp set status ", state, " value=", !res);
    AlarmUtil.getSD_STATUS_EJECTED().then((result) => {
      LogUtil.logOnAll("getSD_STATUS_EJECTED by onePopUp=", JSON.stringify(result));
      res = result[0].value || CameraConfig.fromSdCardErrorPush ? false : res;
      if (Device.isReadonlyShared) {
        res = true;
      }
      this.setState({ [state]: !res }, () => {
        if (res) {
          this.sdCardabnormal();//sd卡异常监测
        }
      });
      if (!res) {
        this._putSD_STATUS_EJECTED();
      }
    }).catch((err) => {
      LogUtil.logOnAll("getSD_STATUS_EJECTED by onePopUp err =", JSON.stringify(err));
      res = CameraConfig.fromSdCardErrorPush ? false : res;
      if (Device.isReadonlyShared) {
        res = true;
      }
      this.setState({ [state]: !res }, () => {
        if (res) {
          this.sdCardabnormal();//sd卡异常监测
        }
      });
      if (!res) {
        this._putSD_STATUS_EJECTED();
      }
    });
    if (!Device.isReadonlyShared) {
      StorageKeys[data] = true;
    }
  }

  _refreshRemoteProps() {
    this._showGBFDialog();
    CameraPlayer.getInstance().getSdcardStatus()
      .then(({ sdcardCode }) => {
        // let result = JSON.parse(res);//res优先转化为json 转化失败则是str
        LogUtil.logOnAll("-=-=-=-=-=-=-=-=-=-=-= sdcardCode:", sdcardCode);
        this.sdcardCode = sdcardCode;
        if (this.sdcardCode == 0 && CameraConfig.fromSdCardErrorPush) {
          Toast.show("sds_format_success");
        }
        this.setState({ sdcardStatusInt: this.sdcardCode });
        if (this.sdcardCode == 2) {
          this.onePopUp("SDCARD_FULL_DIALOG", 'sdcardFullDialog');
        } else if (this.sdcardCode == CameraPlayer.SD_CARD_TOO_SMALL_CODE) {
          this.onePopUp("SDCARD_SMALL_CAPACITY", 'sdcardSmallDialog');
        } else if (this.sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE
          || this.sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE
          || this.sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE
          || this.sdcardCode == 3) {
          this.onePopUp(`SDCARD_FORMAT_DIALOG_${ sdcardCode }`, 'sdcardFormatDialog');
        }
        // this.setState({ sdcardStatusInt: CameraPlayer.SD_CARD_NEED_FORMAT_CODE });
      })
      .catch(({ sdcardCode, error }) => {
        // fix MIIO-40229
        // error in this form {"error": {"code": -2003, "domain": "MiHomeNetworkErrorRemote", "localDescription": "The operation couldn’t be completed. (MiHomeNetworkErrorRemote error -2003.)"}, "message": "callMethod failedError Domain=MiHomeNetworkErrorRemote Code=-2003 \"(null)\" UserInfo={ot_cost=1570, id=10, code=-2003, net_cost=71, exe_time=100, message=default error, otlocalts=1598861669714605, error={code = \"-2003\"}}"};
        if (typeof (sdcardCode) === 'number' && sdcardCode >= 0) {
          this.sdcardCode = sdcardCode;
        }

        console.log("request sdcard status error", error);
      });


  }

  _renderVisitInfoDialog() {
    let message =
      Device.isFamily ?
        LocalizedStrings["visit_record_tip1"] :
        (
          Device.isReadonlyShared
            ?
            LocalizedStrings["visit_record_tip2"]
            :
            LocalizedStrings["visit_record_tip1"]
        );

    // if(this.state.isVisitShow)
    return (
      <MessageDialog
        visible={ this.state.isVisitShow }
        title={ LocalizedStrings["device_visit_record"] }
        message={ message }
        messageStyle={ { textAlign: 'center', backgroundColor: 'white' } }
        buttons={ [
          {
            text: LocalizedStrings["offline_divice_ok"],
            style: { color: 'lightpink' },
            // onLongPress: () => alert('aa'),
            // backgroundColor: { bgColorNormal: 'red', bgColorPressed: 'green',},
            callback: () => {
              this.setState({
                isVisitShow: false
              });
              StorageKeys.IS_VISIT_PUSH_SHOWN = true;
            }
          }
        ] }
        onDismiss={ () => {
          this.setState({ isVisitShow: false });
        } }
      />

    );
  }

  _checkNasVersion() {
    LogUtil.logOnAll("nas_get_config-=-=-=-=start checkNasVersion=", CameraConfig.checkNasVersion);
    if (CameraConfig.isNASV123(Device.model) && CameraConfig.checkNasVersion) {
      RPC.callMethod("nas_get_config", {})
        .then((res) => {
          LogUtil.logOnAll("nas_get_config-=-=-=-=", JSON.stringify(res));
          let support_type = res.result?.support_type || 1;
          let share_type = res.result?.share?.type || -1; // -1 表示没有连接的设备
          if (support_type == 1 || share_type == 1) {
            CameraConfig.nasUpgradeTips = -1; // 无提示
            if (support_type === 1) {
              if (share_type !== 1) {
                CameraConfig.nasUpgradeTips = 1; // 提示摄像机固件升级
              } else {
                CameraConfig.nasUpgradeTips = 2; // 都要升级的提示
              }
            } else {
              if (share_type === 1) {
                CameraConfig.nasUpgradeTips = 3; // 提示nas设备需要升级
              }
            }
            if (CameraConfig.nasUpgradeTips != CameraConfig.nasUpgradeTipsShown) {
              this.setState({ showNasRedDot: true });
            } else {
              this.setState({ showNasRedDot: false });
            }
          } else {
            this.setState({ showNasRedDot: false });
          }
        }).catch((err) => {
        LogUtil.logOnAll("LiveVideoPageV2", `nas_get_config failed: ${ JSON.stringify(err) }`);
        this.setState({ showNasRedDot: false });
      });
      CameraConfig.checkNasVersion = false;
      CameraConfig.nasUpgradeDlgBtnChecked = false;
    }
  }
}

const styles = StyleSheet.create({
  main: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    width: "100%",
    height: "100%"
  },

  controlLayout: {
    position: "relative",
    display: "flex",
    zIndex: 1,
    backgroundColor: "#EEEEEE",
    width: "100%",
    flexDirection: "column",
    flexWrap: 'nowrap',
    flexGrow: 1
  },

  panelOptionsViewLayout: {
    display: "flex",
    width: "100%",
    height: "100%",
    // marginTop: kScreenHeight >= 700 ? 25 : 5,
    flexGrow: 1,
    flexDirection: "column"
  },

  bgImageStyle: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0
  },

  panelOptionItemLayout: {
    display: "flex",
    position: "relative",
    width: "100%",
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
    paddingLeft: 30,
    paddingRight: 30
  },

  // fixContolView: { 
  //   display: "flex", 
  //   position: "absolute", 
  //   top: 0,
  //   zIndex: 2, 
  //   height: fixControlBarHeight, 
  //   width: "100%", 
  //   flexDirection: "row", 
  //   alignItems: "center", 
  //   justifyContent: this.isPtz ? "space-around" : "center",
  //   backgroundColor: "#ffffff",
  //   borderBottomLeftRadius: 30, 
  //   borderBottomRightRadius: 30
  // },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBarPortrait: {
    display: "flex",
    position: "absolute",
    bottom: 0,
    marginBottom: 5,
    width: "100%",
    height: iconButtonSize,
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 25,
    paddingRight: 25,
    justifyContent: "space-between",
    backgroundColor: "transparent"
  },

  videoControlBarLandscape: {
    display: "flex",
    width: "100%",
    paddingRight: 22,
    flexDirection: "row",
    justifyContent: "flex-end"
  },

  videoControlBarItem: {
    width: iconButtonSize,
    height: iconButtonSize,
    marginBottom: 5,
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  videoControlBarItemLandscape: {
    width: iconButtonSize,
    height: iconButtonSize,
    display: "flex",
    marginLeft: 22,
    justifyContent: "center",
    alignItems: "center"
  },

  landscapeButton: {
    width: 50,
    height: 50,
    marginTop: kWindowWidth < 400 ? 50 : 35
  },

  landscapeLongPressButton: {
    display: "flex",
    width: 80,
    height: 50,
    marginTop: 35,
    alignItems: "flex-end",
    justifyContent: "flex-end"
  },

  optionNewTagContainerStyle: {
    marginLeft: 5,
    marginTop: 0,
    width: 16,
    height: 16,
    borderRadius: 7,
    justifyContent: "center",
    alignItems: "center",

    backgroundColor: "#FF5D45FF"
  },

  optionNewTagStyle: {
    fontSize: kIsCN ? 9 : 7,
    textAlign: "center",
    textAlignVertical: "center",
    color: "#ffffff",
    fontWeight: "bold",
    backgroundColor: "#FF5D45FF"
  }

});
