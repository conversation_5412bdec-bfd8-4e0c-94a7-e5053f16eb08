import { Device } from "miot";
import CameraConfig from "./CameraConfig";

export default class VersionUtil {
  static Model_Camera_V1 = "mijia.camera.v1";
  static Model_Camera_V3 = "mijia.camera.v3";
  static Model_Chuangmi_021 = "chuangmi.camera.ipc021";
  static Model_Chuangmi_021a04 = "chuangmi.camera.021a04";
  static Model_Chuangmi_026 = "chuangmi.camera.ip026c";
  static Model_Chuangmi_029 = "chuangmi.camera.ip029a";
  static Model_Chuangmi_009 = "chuangmi.camera.ipc009";
  static Model_Chuangmi_019 = "chuangmi.camera.ipc019";
  static Model_Chuangmi_022 = "chuangmi.camera.ipc022";

  static settingsImgPath = "";
  static Model_chuangmi_039a01 = "chuangmi.camera.039a01";
  static Model_chuangmi_049a01 = "chuangmi.camera.049a01";
  static Model_chuangmi_051a01 = "chuangmi.camera.051a01";
  static Model_chuangmi_086ac1 = "chuangmi.camera.086ac1";

  static isAiCameraModel(model) {
    return model === this.Model_Chuangmi_022 || model === this.Model_chuangmi_051a01;
  }

  /*是否支持智能看护(电子围栏)*/
  static isSupportSmartMonitor(model){
    return model === this.Model_chuangmi_051a01;
  }
  // 添加判断022 051
  static is022Model (model) {
    if ( model === this.Model_Chuangmi_022 ) {
      return true
    } else if ( model === this.Model_chuangmi_051a01 ) {
      return false
    }
  }

  static is009NotSupportCloud(model, version) {
    if (model != this.Model_Chuangmi_009) {
      return false;
    }
    let targetVersion = "3.4.2_0200";
    return !(version == targetVersion || this.isVersionBiggerThanExpected(version, targetVersion));
  }

  static CameraVolumeVersion(model, version) {
    if (CameraConfig.Model_xiaomi_c01a01 == Device.model) {
      if (this.isVersionBiggerEqualThanExpected(version, '5.1.6_1056')) {
        return true;
      } else {
        return false;
      }
    }
    if (model == CameraConfig.Model_xiaomi_c01a02) {
      if (this.isVersionBiggerEqualThanExpected(version, '5.1.2_2042')) {
        return true;
      } else {
        return false;
      }
    }
    if (model == CameraConfig.Model_chuangmi_086ac1) {
      return true;
    }
    return true;
  }
  static getCurrentFirmWareSupportMiss(device) {
    let version = device.lastVersion;
    let model = device.model;
    if (model != "mijia.camera.v3") {
      return true;// 默认时miss吧
    }
    if (version == null || version == "") {
      return true;
    }
    let targetVersion = "3.5.1_0070";
    if (targetVersion.indexOf("_") > 0 && version.indexOf("_") > 0) {
      targetVersion = targetVersion.substring(targetVersion.indexOf("_") + 1);
      version = version.substring(version.indexOf("_") + 1);
      let targetVersionArray = targetVersion.split(".");
      let versionArray = version.split(".");
      let minLength = Math.min(targetVersionArray.length, versionArray.length);
      let diff = 0;
      let index = 0;
      while (index < minLength && (diff = (Number.parseInt(versionArray[index] - Number.parseInt(targetVersionArray[index])))) == 0) {
        index++;
      }
      if (diff == 0) {
        for (; index < versionArray.length; index++) {
          if (Number.parseInt(versionArray[index]) > 0) {
            return true;
          }
        }

        for (; index < targetVersionArray.length; index++) {
          if (Number.parseInt(targetVersionArray[index]) > 0) {
            return false;
          }
        }

      } else {
        if (diff > 0) {
          return true;
        } else {
          return false;
        }
      }

    } else {
      return true;
    }

    return true;
  }

  static judgeIsMiss(Device) {
    console.log(Device.lastVersion);
    if (Device.model === "mijia.camera.v3") {
      return this.isVersionBiggerThanExpected(Device.lastVersion, "3.5.1_0070");
    } else if (Device.model === "mijia.camera.v1") {
      return this.isVersionBiggerThanExpected(Device.lastVersion, "3.5.8_0999");
    } else if (Device.model === this.Model_Chuangmi_009) {
      return this.isVersionBiggerThanExpected(Device.lastVersion, "3.5.7_0399");
    } else if (Device.model === this.Model_Chuangmi_019) {
      return this.isVersionBiggerThanExpected(Device.lastVersion, "3.5.7_0399");
    }
    return true;// 等后面添加啰。
  }

  static judgeIsV1(model) {
    if (model === this.Model_Camera_V1) {
      return true;
    }
    return false;
  }

  static judgeIs009(model) {
    if (model === this.Model_Chuangmi_009) {
      return true;
    }
    return false;
  }

  static judgeIs019(model) {
    if (model === this.Model_Chuangmi_019) {
      return true;
    }
    return false;
  }

  static judgeIsV3(model) {
    if (model === this.Model_Camera_V3) {
      return true;
    }
    return false;
  }

  static judgeIs021(model) {
    if (model === this.Model_Chuangmi_021 || model === this.Model_Chuangmi_021a04) {
      return true;
    }
    return false;
  }

  static judgeIs029(model) {
    if (model === this.Model_Chuangmi_029) {
      return true;
    }
    return false;
  }

  static isUsingSpec(model) {
    if (model === this.Model_Chuangmi_021a04 
      || model == this.Model_Chuangmi_026
      || model == CameraConfig.Model_chuangmi_026c02
      || model == CameraConfig.Model_chuangmi_026c05
      || model === CameraConfig.Model_chuangmi_046a01
      || model == this.Model_Chuangmi_029
      || model == CameraConfig.Model_chuangmi_029a02
      || this.isAiCameraModel(model)
      || model === this.Model_chuangmi_039a01
      || model === CameraConfig.Model_chuangmi_039a04
      || model == CameraConfig.Model_chuangmi_069a01
      || model === this.Model_chuangmi_049a01
      || model === CameraConfig.Model_chuangmi_022
      || model === CameraConfig.Model_xiaomi_c01a01
      || model === CameraConfig.Model_xiaomi_c01a02
      || model === CameraConfig.Model_chuangmi_086ac1
    ) {
      return true;
    } else {
      return false;
    }
  }

  static isVersionBiggerThanExpected(version, expectedVersion) {
    return this.calculateVersionValue(version) - this.calculateVersionValue(expectedVersion) > 0;
  }
  static isVersionBiggerEqualThanExpected(version,expectedVersion) { //当前版本大于等于目标版本
    return this.calculateVersionValue(version) - this.calculateVersionValue(expectedVersion)>=0;
  }
  static isVersionBiggerThanExpectedV2(version, expectedVersion) {
    return this.calculateVersionValue(version) - this.calculateVersionValue(expectedVersion);
  }

  /**
   * 将形如 x.x.x_A1_A2 按照_分割，得到x.x.x,A1,A2的版本，舍弃x.x.x,计算A2+A1*10，得到一个值，如果A1不能转化成数字，则按位取ascii码与'0'的差值得到一个数字当作A1, A2同理。
   * @param {*} version
   */
  static calculateVersionValue(version) {
    if (version == null) {
      return 0;
    }
    let strArray = version.split(/[_]+/);
    let length = strArray.length;
    let multi = 1;
    let sum = 0;
    for (let i = length - 1; i >= 1; i--) {
      let item = strArray[i];
      try {
        let num = Number.parseFloat(item);
        sum += num * multi;
      } catch (exception) {
        let itemLength = item.length;
        let itemMulti = 1;
        let itemSum = 0;
        for (let j = itemLength - 1; j >= 0; j--) {
          let char = item.charCodeAt(j) - "0".charCodeAt();
          itemSum += (char * itemMulti);
          itemMulti *= 10;
        }
        sum += itemSum;
      }
      multi *= 10;
    }
    return sum;
  }
  static calculateVersionValueV2(version) {
    if (version == null) {
      return { len: 3, sum: 0 };
    }
    let strArray = version.split('_');
    let multi = 1;
    let sum = 0;
    let items = strArray[0].split('.');
    let length = items.length;
    try {
      for (let i = length - 1; i >= 0; i--) {
        let num = Number.parseFloat(items[i]);
        sum += num * multi;
        multi *= 10;
      }
    } catch (e) {
      return { len: length, sum: 0 };
    }

    return { len: length, sum: sum };
  }

  static isFirmwareSupportColloctPrivacyLog(model) {
    let version = Device.lastVersion;
    // console.log("isFirmwareSupportColloctPrivacyLog :", version);
    let result = false;
    if (model == this.Model_Camera_V1) {
      if (VersionUtil.isVersionBiggerThanExpected(version, '4.0.9_1002')) {
        result = true;
      }
    } else {
      // https://jira.n.xiaomi.com/browse/IPC-326
      let res = VersionUtil.calculateVersionValueV2(version);
      let target = VersionUtil.calculateVersionValueV2('5.0.3_1000');
      if (res.len == 4) {
        target = VersionUtil.calculateVersionValueV2('5.0.3.0_1000');
      }
      console.log('firmwaire privacy log: ', version, res, target);
      if (res.sum >= target.sum) {
        result = true;
      }
    }
    return result;
  }

  static isFirmwareSupportNas(model) {
    let version = Device.lastVersion;
    let result = true;
    if (model == this.Model_Camera_V1) {
      if (!VersionUtil.isVersionBiggerThanExpected(version, '3.3.10_0145')) {
        result = false;
      }
    }
    if (CameraConfig.isCamera039(model) || Device.model == this.Model_chuangmi_069a01) {
      result = false;
    }

    return result;
  }

  /**
   * 老版本固件设备不支持云存， 但支持看家
   * @param {*} model 
   * @returns 
   */

  static isFirmwareSupportCloud(model) {
    let version = Device.lastVersion;
    let result = true;
    if (model == this.Model_Camera_V1) {
      if (!VersionUtil.isVersionBiggerThanExpected(version, '3.5.1_0173')) {
        result = false;
      }
    }
    if (this.is009NonCloudVersion(model, version)) {
      result = false;
    }
    return result;
  }

  static is009NonCloudVersion(model, version) {
    let result = false;
    if (model == this.Model_Chuangmi_009) {
      if (!VersionUtil.isVersionBiggerThanExpected(version, '3.4.2_0200')) {
        result = true;
      }
    }
    return result;
  }
}