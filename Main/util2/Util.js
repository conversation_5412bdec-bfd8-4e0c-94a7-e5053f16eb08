import { Device, Service, Host } from "miot";
import API, { CloudApi, CameraDomain } from "./API";
import DateFormatter from './DateFormater';
import { Platform, Dimensions, NativeModules } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Order } from "../framework/EventLoaderInf";
import { DarkMode } from 'miot';
import { CldDldTypes } from '../framework/CloudEventLoader';
import dayjs from 'dayjs';
import JSONbig from 'json-bigint';
import LogUtil from "../util/LogUtil";
import { Event } from "../config/base/CfgConst";
import CameraConfig from "../util/CameraConfig";

const TAG = "Util";

const LangMap = { zh: "zh-cn", zh_hk: "zh-hk", zh_tw: "zh-tw" };
const WeekDayMap = { "Mo": "mon", "Tu": "tue", "We": "wed", "Th": "thu", "Fr": "fri", "Sa": "sat", "Su": "sun" };

// small icon is not used yet
const PassSmIc = require("../../resources2/images/icon_alarm_pass_small.png");
const StaySmIc = require("../../resources2/images/icon_alarm_stay_small.png");
const FaceSmIc = require("../../resources2/images/icon_alarm_detect_small.png");
const DestroySmIc = require("../../resources2/images/icon_alarm_rm_small.png");
const BellSmIc = require("../../resources2/images/icon_alarm_bell_small.png");
const SoundSmIc = require("../../resources2/images/icon_alarm_sound_small.png");
const UnKnownFaceSmIc = require("../../resources2/images/icon_unknown_detect_small.png");

const ObjectMvIc = require("../../Resources/Images/e_filter_list_object_move.png");
const PeopleMvIc = require("../../Resources/Images/e_filter_list_people_move.png");
const DefaltIc = require("../../Resources/Images/e_filter_list_all.png");
const AIIc = require("../../Resources/Images/e_filter_list_ai.png");
const BabyCryIc = require("../../Resources/Images/e_filter_list_baby_cry.png");
const FaceIc = require("../../Resources/Images/e_filter_list_face.png");
const louderSoundIc = require("../../Resources/Images/e_filter_list_loud_sound.png");
const UnKnownFaceIc = require("../../resources2/images/icon_unknown_detect.png");
const PetIc = require("../../Resources/Images/e_filter_list_pet.png");
const CarmeraCalling = require("../../Resources/Images/e_filter_list_camera_calling.png");
const CarmeraCallingV2 = require("../../Resources/Images/e_filter_list_camera_voice_video_calling.png");
const commonParam = {
  did: Device.deviceID,
  region: Host.locale.language.includes("en") ? "US" : "CN"
};
// i2  de  us sg ru cn一共六个集群，这里是model支持的服务集群列表
export const CloudServerCluster = [
  "DE", "CN", "SG","US"
];

export const GermanCluster=[
  'DK', 'UA', 'BG', 'HR', 'IS', 'LI',
  'HU', 'LU', 'RS', 'CY', 'AT', 'GR',
  'DE', 'IT', 'LV', 'NO', 'CZ', 'SK',
  'SI', 'BE', 'FR', 'PL', 'BA', 'IE',
  'EE', 'SE', 'CH', 'BY', 'LT', 'RO',
  'FI', 'GB', 'NL', 'PT', 'ES', 'AL',
  'MT'
];

const EvOrder = {
  'CameraCalling': 110,
  "BabyCry": 100,
  "KnownFace": 80,
  "Face": 60,
  "PeopleMotion": 50,
  "Pet": 35,
  "ObjectMotion": 30,
  "AI": 120
};

const EvIcoOrder = {
  'CameraCalling': 110,
  "BabyCry": 100,
  "KnownFace": 80,
  "Face": 60,
  "PeopleMotion": 50,
  "Pet": 35,
  "LouderSound": 32,
  "ObjectMotion": 30,
  "AI": 120
};

export const EvMap = { // 要添加 识别到宠物的文案
  'CameraCalling':{ des: Device.model == "chuangmi.camera.086ac1" ? LocalizedStrings['voice_video_call'] : LocalizedStrings['one_key_call_event_name'],   des_in_label: Device.model == "chuangmi.camera.086ac1" ? LocalizedStrings['voice_video_call'] : LocalizedStrings['one_key_call_event_name'],  icon: { norm: Device.model == "chuangmi.camera.086ac1" ? CarmeraCallingV2 : CarmeraCalling, small: null },    textActive: "#4882FF" },
  'Pet':          { des: LocalizedStrings['pet_desc'],                  des_in_label: LocalizedStrings.pet_desc,                 icon: { norm: PetIc, small: BellSmIc },         textActive: "#FDC541" },
  'ObjectMotion': { des: LocalizedStrings['event_desc_obj_motion'],     des_in_label: LocalizedStrings.event_desc_obj_motion,     icon: { norm: ObjectMvIc, small: BellSmIc },    textActive: "#FDC541" },
  'PeopleMotion': { des: LocalizedStrings['event_desc_people_motion'],  des_in_label: LocalizedStrings.event_desc_people_motion,  icon: { norm: PeopleMvIc, small: PassSmIc },    textActive: "#44CECA" },
  'BabyCry':      { des: LocalizedStrings['baby_cry_desc'],             des_in_label: LocalizedStrings.event_desc_baby_cry,       icon: { norm: BabyCryIc, small: DestroySmIc },  textActive: "#9B91FF" },
  'Face':         { des: LocalizedStrings['event_desc_unknown_people'], des_in_label: LocalizedStrings.event_desc_unknown_people, icon: { norm: UnKnownFaceIc, small: UnKnownFaceSmIc }, textActive: "#2DB0FF" },
  'KnownFace':    { des: LocalizedStrings['event_desc_known_people'],   des_in_label: LocalizedStrings.alarm_event_face_face,     icon: { norm: FaceIc, small: FaceSmIc },        textActive: "#2DB0FF" },
  'AI':           { des: LocalizedStrings['event_desc_ai_scene'],       des_in_label: LocalizedStrings.event_desc_ai_scene,       icon: { norm: AIIc, small: StaySmIc },          textActive: "#7DA6E0" },
  'LouderSound':  { des: LocalizedStrings['loud_desc'],                 des_in_label: LocalizedStrings.loud_desc,                 icon: { norm: louderSoundIc, small: SoundSmIc },     textActive: "#F29E60" },
  'Default':      { des: LocalizedStrings['all_events'],                des_in_label: LocalizedStrings.all_events,                icon: { norm: DefaltIc, small: null },          textActive: "#4882FF" }
};

export const EvArray = [ // 要添加 识别到宠物的文案
  { key: 'Default' },
  { key: 'ObjectMotion' },
  { key: 'Pet' },
  { key: 'PeopleMotion' },
  { key: 'LouderSound' },
  { key: 'BabyCry' },
  { key: 'Face' },
  // { key: 'KnownFace' },
  { key: 'CameraCalling' },
  { key: 'AI' },
  // { key: 'AbnormalSound' },
  // { key: "LouderSound" },
  // { key: "FenceIn" },
  // { key: "FenceOut" }
];
export const DayInMilli = 24 * 60 * 60 * 1000;

export default class Util {


  static getLanguage() {
    let lang = Host.locale.language || "en_us";
    if (LangMap[lang]) {
      lang = LangMap[lang];
    }
    let language = lang;
    if (language == "zh-hk") {
      require('dayjs/locale/zh-hk');
    } else if (language == "zh-tw") {
      require('dayjs/locale/zh-tw');
    } else if (language == "zh-cn") {
      require('dayjs/locale/zh-cn');
    } else if (language == "en") {
      require('dayjs/locale/en');
    } else if (language == "ko") {
      require('dayjs/locale/ko');
    } else if (language == "ru") {
      require('dayjs/locale/ru');
    } else if (language == "es") {
      require('dayjs/locale/es');
    } else if (language == "fr") {
      require('dayjs/locale/fr');
    } else if (language == "it") {
      require('dayjs/locale/it');
    } else if (language == "de") {
      require('dayjs/locale/de');
    } else if (language == "id") {
      require("dayjs/locale/id");
    } else if (language == "ja") {
      require("dayjs/locale/ja");
    } else if (language == "th") {
      require("dayjs/locale/th");
    } else if (language == "pl") {
      require("dayjs/locale/pl");
    } else if (language == "vi") {
      require("dayjs/locale/vi");
    } else if (language == "nl") {
      require("dayjs/locale/nl");
    } else if (language == "pt") {
      require("dayjs/locale/pt");
    } else if (language == "tr") {
      require("dayjs/locale/tr");
    }

    return lang;
  }

  static isAndroid() {
    return Platform.OS == "android";
  }


  static zeroPad(aNum, aBase) {
    let len = (String(aBase).length - String(aNum).length) + 1;
    return len > 0 ? new Array(len).join('0') + aNum : aNum;
  }

  static fmtStr(aFmt, ...args) {
    let jStr = aFmt.replace(/{([0-9]+)}/g,
      function (sub) {
        return args[parseInt(sub[1])];
      });
    return jStr;
  }

  static fmtSize(aSize) {
    let unitArr = new Array("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB");
    let index = Math.floor(Math.log(Math.max(1, aSize)) / Math.log(1024));
    let base = Math.pow(1024, index);
    let size = aSize / base;
    if (aSize % base / base > 0.01) {
      size = size.toFixed(2);// 保留的小数位数
    } else {
      size = size.toFixed(0);
    }

    return size + unitArr[index];
  }

  static concat(aResType, ...arrays) {
    let totalLength = 0;
    for (let arr of arrays) {
      totalLength += arr.length;
    }
    let result = new aResType(totalLength);
    let offset = 0;
    for (let arr of arrays) {
      result.set(arr, offset);
      offset += arr.length;
    }
    return result;
  }

  static isString(aVal) {
    return "string" === typeof aVal;
  }




  static isEarlierOrEqualTo(timeStr1, timeStr2) {
    // 早于 - 返回 -1
    // 晚于 - 返回 1
    // 等于 - 返回 0
    // convert int
    const hour1 = timeStr1.split(":")[0] * 1;
    const minute1 = timeStr1.split(":")[1] * 1;
    const hour2 = timeStr2.split(":")[0] * 1;
    const minute2 = timeStr2.split(":")[1] * 1;

    let isEarlier = -1;
    if (hour1 < hour2) {
      isEarlier = -1;
    } else if (hour1 === hour2) {
      if (minute1 < minute2) {
        isEarlier = -1;
      } else if (minute1 === minute2) {
        isEarlier = 0;
      } else {
        isEarlier = 1;
      }
    } else {
      isEarlier = 1;
    }

    return isEarlier;
  }


  static byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24;
  }

  static eventRead(aItem) {
    let params = {
      "did": Device.deviceID,
      "fileId": aItem.fileId,
      "offset": aItem.offset ? aItem.offset : 0
    };
    API.instance().post('/common/app/markRead', 'business.smartcamera', params).then((ret) => {
      console.log(TAG, "eventRead success", ret);
    })
      .catch((err) => {
        console.log(TAG, "eventRead failed", err);
      });
  }

  /*
  aDate: end time
  aRange: fetch range from now//current normal 7 days vip 30 days;
  
  Asc Order
  arg: bgn end
  return bgn[ev ev, ev)endtime           end
  
  Desc Order
  arg: bgn end
  return bgn           endtime(ev ev, ev]end
  */
  static getAllEvent(aDate, aRange, aEvent = 'Default', aIsMore = false, aLimit = 20, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let bgn = null;
    let end = null;
    let now = new Date();
    let date = new Date(aDate);

    if (aIsMore) {
      if (Order.Desc == aOrder) {
        if (aRange == 0) {
          bgn = dayjs(aDate).hour(0).minute(0).second(0).subtract(aRange, "days");
        } else {
          bgn = dayjs().hour(0).minute(0).second(0).subtract(aRange, "days");
        }
        end = dayjs(aDate);
      } else {
        bgn = dayjs(aDate);
        end = dayjs();
      }
    } else {
      if (Order.Desc == aOrder) {
        // now - range
        if (aRange == 0) {
          bgn = dayjs(aDate).hour(0).minute(0).second(0).subtract(aRange, "days");
        } else {
          bgn = dayjs().hour(0).minute(0).second(0).subtract(aRange, "days");
        }
        end = dayjs(aDate).hour(23).minute(59).second(59);
      } else {
        bgn = dayjs(aDate).add(1, "days").hour(0).minute(0).second(0);
        end = dayjs();
      }
    }
    let endTime = end.valueOf();
    let beginTime = bgn.valueOf();
    let params = {
      "model": Device.model,
      "eventType": aEvent,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": aLimit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    if (type == CldDldTypes.Files) {
      return this.requestFiles(params, aPlayCfg);
    } else {
      return this.requestEvents(params, aPlayCfg);
    }
  }


  // 拿到某一天时间的（从0点到24点）看家事件列表
  static getEventList(date, event = 'Face', isMore = false, limit = 20, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let now = new Date();
    let isToday = Math.floor(now.getTime() / DayInMilli) == Math.floor(date.getDate() / DayInMilli);
    let bgn = null;
    let end = null;

    bgn = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
    console.log("bgn:",bgn)
    end = isToday ? now : new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
    console.log("end:",end)
    if (isMore) {
      end = date;
    }
    let beginTime = bgn.getTime();
    let endTime = end.getTime();
    let params = {
      "model": Device.model,
      "eventType": event,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": limit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    if (type == CldDldTypes.Files) {
      return this.requestFiles(params, aPlayCfg);
    } else {
      return this.requestEvents(params, aPlayCfg);
    }

  }
  // 拿到几天时间的看家列表
  static getEventListRange(date, event = 'Face', isMore = false, limit = 20, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let now = new Date();
    // let isToday = Math.floor(now.getTime() / DayInMilli) == Math.floor(date.getDate() / DayInMilli);
    let bgn = null;
    let end = null;

    bgn = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
    console.log("bgn:", bgn);
    end = now;
    console.log("end:", end);
    if (isMore) {
      end = date;
    }
    let beginTime = bgn.getTime();
    let endTime = end.getTime();
    let params = {
      "model": Device.model,
      "eventType": event,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": limit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    if (type == CldDldTypes.Files) {
      return this.requestFiles(params, aPlayCfg);
    } else {
      return this.requestEvents(params, aPlayCfg);
    }

  }


  static getEventList1(beginTime, endTime, event = 'Default', isMore = false, limit = 50, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let bgn = beginTime;
    let end = endTime;
    beginTime = bgn.getTime();
    endTime = end.getTime();
    let params = {
      "model": Device.model,
      "eventType": event,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": limit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    return this.requestEvents(params, aPlayCfg);

  }

  static getEventsByFileId(did, model, fileId, isAlarm, aPlayCfg = null, evType = "Default") {
    console.log(TAG, "getEventsByFileId");
    let params = {
      'did': did,
      'model': model,
      'isAlarm': isAlarm,
      'fileId': fileId
    };
    return this.requestEventByFileId(params, aPlayCfg, evType);
  }

  static async requestFiles(aParams, aPlayCfg) {
    // console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/cloudlist', 'business.smartcamera', aParams);
    if (ret.code != 0 || ret.data == null) {
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        let eventType = unit.eventType;
        console.log(TAG, "file unit type", unit.eventType, unit.fileId, unit.offset, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"));
        let item = {
          createTime: unit.createTime,
          eventTime: DateFormatter.instance().format(unit.createTime),
          type: eventType,
          desc: this.getDescFromType(eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          duration: unit.duration, // only for file list
          isAlarm: unit.isAlarm,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(eventType)) {
          faceMap[unit.fileId] = item;
          faceArr.push(unit.fileId);
        }
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          for (let meta of metaRet.fileIdMetaResults) {
            let fid = meta.fileId;
            // console.log(TAG, "meta info", meta);
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[fid];
            if (!itemWithFace) continue;
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null };
            let extraDes = this.getDescFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
        } catch (aErr) {
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEvents(aParams, aPlayCfg) {
    // console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/eventlist', 'business.smartcamera', aParams);
    Service.smarthome.reportLog(Device.model, "/common/app/get/eventlist"+ret);
    if (ret.code != 0 || ret.data == null) {
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        let eventType = unit.eventType;
        console.log(TAG, "event unit type", eventType, unit.fileId, unit.offset, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"));
        let item = {
          createTime: unit.createTime,
          eventTime: DateFormatter.instance().format(unit.createTime),
          type: eventType,
          desc: this.getDescFromType(eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          isAlarm: unit.isAlarm,
          isShowImg: unit.isShowImg,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(eventType)) {
          faceMap[unit.isAlarm ? `${unit.fileId}` : `${unit.fileId}:${unit.offset}`] = item;
          faceArr.push(unit.fileId);
        }
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          // console.log(TAG, "meta info", JSON.stringify(metaRet));
          let needWritLogs = false;
          for (let meta of metaRet.fileIdMetaResults) {
            let fid = meta.fileId;
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[`${fid}:${meta.offset}`];
            if (!itemWithFace) itemWithFace = faceMap[`${fid}`];
            if (!itemWithFace) {
              needWritLogs = true;
              LogUtil.logOnAll(TAG, "face meta info error unMatched item = ", meta);
              continue;
            }
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null, figureId: faceM.figureId };

            let extraDes = this.getDescFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
          if (faceArr.length != metaRet.fileIdMetaResults.length || needWritLogs) {
            LogUtil.logOnAll(TAG, "face meta info error params=", JSON.stringify({ did: Device.deviceID, model: Device.model, fileIds: { fileIds: faceArr } }),
              "resps=", JSON.stringify(metaRet));
            Object.keys(faceMap).map((key) => {
              let unit = faceMap[key];
              LogUtil.logOnAll(TAG, "face meta info error item video", unit.type, unit.fileId, unit.offset, unit.isAlarm, unit.desc, "@", unit.createTime, this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"));
            });
          }
        } catch (aErr) {
          Service.smarthome.reportLog(Device.model, "/common/app/get/eventlist_aErr"+aErr);
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEventByFileId(aParams, aPlayCfg, evType) {
    console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/fileIdEvents', 'business.smartcamera', aParams);
    console.log(TAG, "requestEvents", ret);
    if (ret.code != 0 || ret.data == null) {
      console.log('requestEventByFileId err', ret.code);
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      console.log(TAG, 'event count: ', playUnits.length);
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        // console.log(TAG, "unit type", unit.eventType, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"), 'evType', evType);
        let eventType = unit.eventType;
        if (evType != "Default" && eventType.indexOf(evType) == -1) {
          continue; // filter of specific event
        }
        let item = {
          createTime: unit.createTime,
          eventTime: this.getMoment(unit.createTime / 1000).format("HH:mm:ss"),
          eventTime2: unit.createTime,
          type: eventType,
          desc: this.getDescFromType(eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          duration: unit.duration, // only for file list
          isAlarm: unit.isAlarm,
          isShowImg: unit.isShowImg,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(eventType) || eventType.indexOf("Face") != -1) {
          faceMap[unit.offset] = item;
          faceArr.push(unit.fileId);
        }

      }
      console.log(TAG, 'event count after filter', evType, eventItems.length);
      eventItems.sort((x, y) => {
        if (x.offset > y.offset) {
          return 1;
        } else {
          return -1;
        }
      });

      let lasttime = -1;
      for (let i = 0; i < eventItems.length; i++) {
        let item = eventItems[i];
        let tDif = 0.0;
        if (lasttime != -1) {
          tDif = (item.eventTime2 - lasttime) / 1.0;
        }
        lasttime = item.eventTime2;
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          for (let meta of metaRet.fileIdMetaResults) {
            let offset = meta.offset;
            // console.log(TAG, "meta info", meta);
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[offset];
            if (!itemWithFace) continue;
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null, figureId: faceM.figureId };
            let extraDes = this.getDescFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
        } catch (aErr) {
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEventTypeByFileId(aParams) {
    console.log(TAG, "requestEventTypeByFileId", aParams);
    let ret = await API.instance().get('/common/app/get/fileIdEvents', 'business.smartcamera', aParams);
    console.log(TAG, "requestEventTypeByFileId", ret);
    if (ret.code != 0 || ret.data == null) {
      console.log('requestEventTypeByFileId err', ret.code);
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventTypes = [];
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        let type = unit.eventType;
        if (eventTypes.indexOf(type) != -1) {
          eventTypes.push(unit.eventType);
        }
      }

      let data = {
        types: eventTypes,
        fileIDs: aParams.fileId
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static getFileMetas(aFileIds) {
    return new Promise((resolve, reject) => {
      API.instance().get("/miot/camera/app/v1/get/fileIdMetas",
        "business.smartcamera",
        { did: Device.deviceID, model: Device.model, fileIds: { fileIds: aFileIds } })
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }


  static getFaceImgUrl(aFaceId) {

    return new Promise((resolve, reject) => {
      Service.miotcamera.getCommonImgWithParams(aFaceId,
        JSON.stringify({ prefix: "business.smartcamera.", method: "GET", path: "/miot/camera/app/v1/get/face/img" }),
        JSON.stringify({ did: Device.deviceID, faceId: aFaceId, model: Device.model }))
        .then((aRet) => {
          // console.log(aRet,'aRet')
          resolve({ uri: `file://${aRet}` });
        }).catch((err) => {
          console.log('这里出错', err)
          LogUtil.logOnAll("FaceImage", "下载人脸" + aFaceId + "出错了，错误原因:" + JSON.stringify(err));
          reject(err);
        });
    });
  }

  // noFail
  static reportFaceRecError(aEv, type) {
    return new Promise((resolve, reject) => {
      API.instance().post("/miot/camera/app/v1/feedback",
        "business.smartcamera",
        { did: Device.deviceID, model: Device.model, fileId: aEv.fileId, isVisible: true, type: type })
        .then((aRet) => {
          resolve(aRet);
        }).catch((err) => {
          resolve(err);
        });
    });
  }

  static hasFaceInfo(aType) {
    return (aType != null && aType.indexOf("Face") != -1
      && aType.indexOf("CameraCalling") < 0 && aType.indexOf("BabyCry") < 0);
  }

  static getTypeByOrder(aType, aOrder) {
    let pType = aType;
    if (aType != null && aType.indexOf(":") != 1) {
      let tArr = aType.split(":");
      pType = tArr.reduce((aPV, aCV) => {
        if (aOrder[aPV] > aOrder[aCV]) {
          return aPV;
        } else {
          return aCV;
        }
      });
    }
    return pType;
  }

  static getPrimaryType(aType) {
    return this.getTypeByOrder(aType, EvOrder);
  }

  static getPrimaryIcoType(aType) {
    return this.getTypeByOrder(aType, EvIcoOrder);
  }

  static getFiltedEvents(aType) {
    let pTypes = this.getEventTypeArray(aType);
    let type1 = null;
    let type2 = null;
    if (pTypes.includes(Event.AI)) {
      type1 = Event.AI;
    } else if (pTypes.includes(Event.BabyCry)) {
      [type1, type2] = this.getFiltedByBabyCry(pTypes);
    } else if (pTypes.includes(Event.Pet) || pTypes.includes(Event.Dog) || pTypes.includes(Event.Cat)) {
      [type1, type2] = this.getFiltedByPet(pTypes);
    } else {
      [type1, type2] = this.getFiltedSingle(pTypes);
    }
    return [type1, type2];
  }

  static getFiltedByBabyCry(pTypes) {
    let type2 = null;
    if (pTypes.includes(Event.KnownFace)) {
      type2 = Event.KnownFace;
    } else if (pTypes.includes(Event.Pet)) {
      type2 = Event.Pet;
    } else if (pTypes.includes(Event.PeopleMotion)) {
      type2 = Event.PeopleMotion;
    } else if (pTypes.includes(Event.ObjectMotion)) {
      type2 = Event.ObjectMotion;
    } 
    return [Event.BabyCry, type2];
  }

  static getFiltedByPet(pTypes) {
    let type2 = null;
    if (pTypes.includes(Event.KnownFace)) {
      type2 = Event.KnownFace;
    } else if (pTypes.includes(Event.PeopleMotion)) {
      type2 = Event.PeopleMotion;
    }
    return [Event.Pet, type2];
  }

  static getFiltedSingle(pTypes) {
    let type1 = Event.ObjectMotion;
    if (pTypes.includes(Event.CameraCalling)) {
      type1 = Event.CameraCalling;
    } else if (pTypes.includes(Event.KnownFace)) {
      type1 = Event.KnownFace;
    } else if (pTypes.includes(Event.PeopleMotion)) {
      type1 = Event.PeopleMotion;
    } else if (pTypes.includes(Event.LouderSound)) {
      type1 = Event.LouderSound;
    } else if (pTypes.includes(Event.ObjectMotion)) {
      type1 = Event.ObjectMotion;
    }
    return [type1, null];
  }

  static getDescFromType(aType, aFaceInfo = null ) {
    let [type1, type2] = this.getFiltedEvents(aType);
    let desc1 = null;
    let desc2 = null;
    if (type1) {
      desc1 = this.getEventDesc(type1, aFaceInfo);
    }
    if (type2) {
      desc2 = this.getEventDesc(type2, aFaceInfo);
    }
    if (desc1 && desc2) {
      return this.joinDesc(desc1, desc2, true);
    } else if(desc1) {
      return desc1;
    } else if(desc2) {
      return desc2;
    } else {
      return '';// error
    }
  }

  static getEventDesc(type, aFaceInfo) {
    let desc = EvMap[type].des_in_label;
    if (type === Event.KnownFace) {
      if (aFaceInfo && aFaceInfo.name) {
        desc = this.fmtStr(EvMap['KnownFace'].des_in_label, aFaceInfo.name);
      }
    }
    return desc;
  }

  static joinDesc(desc1, desc2, exclamatoryMark) {
    desc1 = this.removeLastExclamatorMark(desc1);
    desc2 = this.removeLastExclamatorMark(desc2);
    let desc = `${ desc1 }${ LocalizedStrings['event_and'] }${ desc2 }`;
    if (exclamatoryMark) {
      return `${ desc }!`;
    }
    return desc;
  }

  static removeLastExclamatorMark(desc) {
    if (desc) {
      let pos = desc.indexOf('!');
      if (pos == desc.length - 1) {
        return desc.substr(0, pos);
      }
    }
    return desc;
  }


  static getEventTypeArray(aType) {
    let pTypes = [];
    if (aType != null) {
      let tArr = aType.split(":");
      pTypes = tArr.map((key) => {
        let res = null;
        switch (key) {
          case 'AI':
            res = Event.AI;
            break;
          case 'CameraCalling':
            res = Event.CameraCalling;
            break;
          case 'BabyCry':
            res = Event.BabyCry;
            break;
          case 'Face':
            res = Event.KnownFace;
            break;
          case 'PeopleMotion':
            res = Event.PeopleMotion;
            break;
          case 'ObjectMotion':
            res = Event.ObjectMotion;
            break;
          case 'Pet':
            res = Event.Pet;
            break;
          case 'Dog':
            res = Event.Dog;
            break;
          case 'Cat':
            res = Event.Cat;
            break;
          case 'LouderSound':
            res = Event.LouderSound;
            break;
        }
        return res;
      });
    }
    return pTypes;
  }

  static getDescFromType_old(type) {
    let pType = this.getPrimaryType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.des;
    } else {
      return LocalizedStrings.alarm_event_pass;
    }
  }
  static getIconByEventType(eType) {
    let ret = EvMap[eType];
    if (ret) {
      return ret.icon.norm;
    } else {
      return DefaltIc;
    }
  }
  static getIconFromType(type, faceName = null) {
    let pType = this.getPrimaryIcoType(type);
    if (pType && pType.indexOf('Face') != -1) {
      pType = "KnownFace";
    }
    let ret = EvMap[pType];
    if (ret) {
      return ret.icon.norm;
    } else {
      return DefaltIc;
    }
  }

  static getSmallIconFromType(type) {
    let pType = this.getPrimaryIcoType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.icon.small;
    } else {
      return null;
    }
  }

  static getActiveColorFromType(type) {
    let pType = this.getPrimaryIcoType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.textActive;
    } else {
      return "#4882FF";
    }
  }

  static isLocalThumb(aUrl, aType = "video") {
    const ct = this.isAndroid() ? "content:"
      : ("image" == aType) ? "miotph:" : "miotvideo:";
    return (aUrl.substr(0, ct.length) == ct);
  }

  static isLocalVideo(aUrl) {
    const ct = this.isAndroid() ? "content:" : "file:";
    return (aUrl.substr(0, ct.length) == ct);
  }



  static getVideoUrl(item) {
    if (this.isLocalVideo(item.fileId)) {
      return Promise.resolve(item.fileId);
    } else {
      return new Promise((resolve, reject) => {
        if (!item.isAlarm) {
          item.isAlarm = false;
        }
        Service.miotcamera.getVideoFileUrl(item.fileId, item.isAlarm, "H265")// codec is not used at all
          .then((res) => {
            resolve(res);
          }).catch((err) => {
            reject(err);
          });
      });
    }
  }

  static checkExist(item) {
    if (this.isLocalVideo(item.fileId)) {
      return Promise.resolve(item.fileId);
    } else {
      return new Promise((resolve, reject) => {
        if (!item.isAlarm) {
          item.isAlarm = false;
        }
        API.instance().post('/common/app/file/delete/status', 'business.smartcamera', { "fileId": item.fileId, "did": Device.deviceID, "isAlarm": item.isAlarm })
          .then((result) => {
            resolve(result);
          })
          .catch((error) => {
            reject(error);
          });
      });
    }
  }

  static deleteVideo(fileIDs = []) {
    let params = {
      fileIds: {
        fileIds: fileIDs
      }
    };
    return new Promise((resolve, reject) => {
      API.instance().post('/common/app/v2/delete/files', 'business.smartcamera', params)
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }


  static async sleep(aTime) {
    await new Promise((aResol) => { setTimeout(aResol, aTime); });
  }

  static getNonce() {
    let now = Date.now();
    // console.log(`now${ now }`);
    return `${now}${Math.random(now)}`;
  }


  static fetchVipStatus() {
    return new Promise((resolve, reject) => {
      API.instance().get("/miot/camera/app/v1/vip/status", "business.smartcamera")
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  static fetchCloudCapacity() {
    return new Promise((resolve, reject) => {
      API.instance().get("/common/app/v1/capacity", "business.smartcamera", { did: Device.deviceID, region: "CN" })
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  static getMoment(aTsInSec) {

    return dayjs.unix(aTsInSec).locale(this.getLanguage());

  }

  static mapWeekString(input) {
    if (WeekDayMap.hasOwnProperty(input)) {
      return WeekDayMap[input];
    }
    return input;
  }

  static _formatSize(bytes) {
    console.log('_formatSize', bytes);
    if (bytes == null || bytes < 0) {// 异常值处理
      return " ";
    }
     return bytes < 1024 ? `${ bytes }B` : (
      bytes < 1024 * 1024 ? `${ (bytes / 1024).toFixed(2) }K` : (
        bytes < 1024 * 1024 * 1024 ? `${ (bytes / 1024 / 1024).toFixed(2) }M` :
          `${ (bytes / 1024 / 1024 / 1024).toFixed(2) }G`
      )
    );
  }

  static async getExistFigure(aFigureN) {
    let queryParams = {
      did: Device.deviceID,
      model: Device.model,
      figureName: aFigureN
    };
    let ret = null;
    try {
      ret = await API.instance().get(CloudApi.QueryFigure, CameraDomain, queryParams);
    } catch (err) {
      console.log(TAG, "no exiting find", err);
    }
    if (ret && "ok" == ret.result.toLowerCase() && ret.data && ret.data.figureId) {
      return ret.data.figureId;
    } else {
      return null;
    }
  }

  static async addFaceToExisting(aFigureId, aFaceId) {
    let addParams = {
      did: Device.deviceID,
      model: Device.model,
      faceId: aFaceId,
      figureId: aFigureId
    };
    console.log(TAG, "commentFace find add face to existing");
    // return await API.instance().post(CloudApi.AddFace, CameraDomain, addParams);
    return await Service.callSmartHomeCameraAPIWithStringParam(CloudApi.AddFace, CameraDomain, true, JSONbig.stringify(addParams));
  }

  static async commentFace(aFigureN, aFaceId) {
    let api = API.instance();
    let queryParams = {
      did: Device.deviceID,
      model: Device.model,
      figureName: aFigureN
    };
    let curApi = null;
    let ret = null;
    let existFigureId = await this.getExistFigure(aFigureN);
    console.log(TAG, "query existed figureId:" + existFigureId);
    if (existFigureId != null) {
      let res = await this.addFaceToExisting(existFigureId, aFaceId);
      res.existFigureId = existFigureId;
      res.data = { figureId: existFigureId };
      console.log(TAG, "add face " + JSONbig.stringify(aFaceId) + " into existed figure:" + JSONbig.stringify(existFigureId) + " return value:" + JSON.stringify(ret));
      return res;
    } else {

      let figParams = {
        did: Device.deviceID,
        model: Device.model,
        figureInfo: aFigureN,
        figureName: aFigureN
      };
      // console.log('faceaa', 'enter commentface');
      curApi = CloudApi.AddFigure;
      try {
        let ret = await api.post(curApi, CameraDomain, figParams);
        console.log(TAG, "add figure:" + aFigureN + " return value:" + ret);

        if ("ok" == ret.result.toLowerCase()) {
          let faceParams = {
            did: Device.deviceID,
            model: Device.model,
            figureId: ret.data.figureId,
            faceId: aFaceId
          };
          // console.log('faceaa', 'addFigure ok, id: ', ret.data.figureId);
          curApi = CloudApi.AddFace;
          let existFigureId = ret.data.figureId;
          // ret = await api.post(curApi, CameraDomain, faceParams);
          ret = await Service.callSmartHomeCameraAPIWithStringParam(curApi, CameraDomain, true, JSONbig.stringify(faceParams))
          ret.existFigureId = existFigureId;
          // console.log('faceaa', 'AddFace result: ', ret);
          console.log(TAG, "add face " + JSONbig.stringify(aFaceId) + " into existed figure:" + JSONbig.stringify(ret.data.figureId) + " return value:" + JSON.stringify(ret));
          ret.data = { figureId: faceParams.figureId };
          return ret;
        } else {
          // let err = `${curApi} failed ${ret}`;
          // let err = {curApi,ret}
          // console.log(TAG, "commentFace error ", err);
          console.log(TAG, 'ret', ret)
          throw ret;
        }

      } catch (Exception) {
        console.log(TAG,'Exception', Exception);
        // throw "error:" + Exception
        throw Exception
      }

    }
  }

  static async modifyFaceComment(aFigureId, aFaceId, aFigureN) {
    let ret = null;
    let existFigureId = await this.getExistFigure(aFigureN);
    if (existFigureId != null && aFigureId != existFigureId) {
      let delParams = {
        did: Device.deviceID,
        model: Device.model,
        faceId: aFaceId,
        figureId: aFigureId
      };
      await API.instance().post(CloudApi.DelFace, CameraDomain, delParams);
      ret = new Promise((resolve, reject) => {
        this.addFaceToExisting(existFigureId, aFaceId).then((res) => {
          res.existFigureId = existFigureId;
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    }
    //就是修改一个名字
    else {
      let modParams = {
        did: Device.deviceID,
        model: Device.model,
        figureId: aFigureId,
        figureInfo: aFigureN,
        figureName: aFigureN
      };
      console.log(TAG, "start modify figure");
      ret = await API.instance().post(CloudApi.ModifyFigure, CameraDomain, modParams);
      ret.data = { figureId: aFigureId };
      console.log(TAG, "modify figure complete", ret);
    }

    return ret;
  }

  static getAllFigures() {
    let delParams = {
      did: Device.deviceID,
      model: Device.model
    };
    return API.instance().get(CloudApi.QueryAllFigure, CameraDomain, delParams);
  }
  // 获取人脸所有数据时 先将faceUrl定义为空对象 避免后期取值出现问题
  static async getAllFigure(needImg = true) {
    let delParams = {
      did: Device.deviceID,
      model: Device.model,
      ...commonParam
    };
    let ret = await Service.callSmartHomeCameraAPIWithStringParam(CloudApi.QueryAllFigure, CameraDomain, false, JSON.stringify(delParams));
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      // console.log(data,"data");
      if (data != null && data.figureInfos != null) {
        let retDat = [];
        for (let fInf of data.figureInfos) {
          let faceUrl = {};
          let name = fInf.figureName;
          let figureId = fInf.figureId
          let figureName = fInf.figureName
          let coverFaceId = fInf.coverFaceId;
          try {
            if (needImg) {
              faceUrl = await this.getFaceImgUrl(fInf.coverFaceId);
            } else {
              faceUrl = "";
            }
            // console.log(TAG, "get face:", name, "with", faceUrl);
          } catch (aExp) {
            console.log(TAG, "get faceUrl failed with", fInf.coverFaceId);
            LogUtil.logOnAll(TAG, "getAllFigure", "get faceUrl failed with", fInf.coverFaceId, aExp);
          }
          retDat.push({ name, faceUrl,figureId,figureName,coverFaceId });
        }
        return retDat;
      }
    } else {
      LogUtil.logOnAll(TAG, "getAllFigure", "empty result", ret);
      throw "empty result";
    }
    return ret;
  }

  static isDark() {
    return "dark" == DarkMode.getColorScheme();
  }

  static isToday(date) {
    let now = dayjs(new Date());
    let TODAY = now.clone().startOf('day');
    let isToday = dayjs(date).isSame(TODAY, 'd');
    return isToday;
  }

  static isYestoday(date) {
    let now = dayjs(new Date());
    let YESTERDAY = now.clone().subtract(1, 'days').startOf('day');
    let isYestoday = dayjs(date).isSame(YESTERDAY, 'd');
    return isYestoday;
  }

  static isDaysAgo(date, days) { // true for a future day
    let now = dayjs(new Date());
    let pickDate = dayjs(date);
    let diff = now.diff(pickDate, 'days');
    return diff >= days;
  }

  static isShowSnapShop() {
    let showSnapShot = true;
    let vv = Host.systemInfo;
    let vversion = parseFloat(vv.sysVersion);
    let isiPhone12 = (vv.mobileModel.indexOf('iPhone13') == 0);
    let isiPadPro = (vv.mobileModel.indexOf('iPad8') == 0);
    let isiPadAir = (vv.mobileModel.indexOf('iPad13') == 0);
    if ((isiPhone12 || isiPadPro || isiPadAir) && vversion >= 14.5) {
      console.log('systeminfo', vv.mobileModel, vv.sysVersion);
      console.log("this.showSnapShot00001", this.showSnapShot);
      showSnapShot = false;
    }
    return showSnapShot;
  }

  static async getFigureFaces(aFigureId) {
    let delParams = {
      did: Device.deviceID,
      model: Device.model,
      figureId: aFigureId
    };
    let ret = await API.instance().get(CloudApi.QueryFigureFaces, CameraDomain, delParams);
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      if (data != null && data.faceInfoMetas != null) {
        let retDat = [];
        for (let fInf of data.faceInfoMetas) {
          let faceUrl = {};
          let faceId = fInf.faceId;
          // try {
          //   faceUrl = await this.getFaceImgUrl(fInf.faceId);
          //   // console.log(TAG, "get face:", "with", faceUrl);
          // } catch (aExp) {
          //   console.log(TAG, "get faceUrl failed with", fInf.faceId);
          // }

          retDat.push({ fInf, faceUrl, faceId });
        }
        return retDat;
      }
    } else {
      LogUtil.logOnAll(TAG, "getFigureFaces", "empty result", ret);
      throw "empty result";
    }
    return ret;
  }

  static async delFaces(aFaceIds, aFigureId,) {
    let delParams = {
      did: Device.deviceID,
      model: Device.model,
      figureId: aFigureId,
      faceIds: { ids: aFaceIds },
    };
    let ret = await API.instance().post(CloudApi.DelFaces, CameraDomain, delParams);
    if (ret != null && 0 == ret.code) {
      return ret
    }

  }

  static async modifyAllFaceComment(aFigureId, aFigureN) {

    let modParams = {
      did: Device.deviceID,
      model: Device.model,
      figureId: aFigureId,
      figureInfo: aFigureN,
      figureName: aFigureN
    };
    console.log(TAG, "start modify figure");
    let ret = await API.instance().post(CloudApi.ModifyFigure, CameraDomain, modParams);
    console.log(TAG, "modify figure complete");


    return ret;
  }
  static async getAllUnmarkFaces(endTime) {
    // let endTime = new Date().getTime()
    let delParams = {
      did: Device.deviceID,
      model: Device.model,
      region: "CN",
      beginTime: 0,
      endTime: endTime,
      limit: 50
    };
    let ret = await API.instance().get(CloudApi.QueryAllUnmarkFaces, CameraDomain, delParams);
    console.log(TAG, "getAllUnmarkFaces detail:" + JSON.stringify(ret));
    if (ret != null && 0 == ret.code) {
      let nextTime = ret.data.nextTime
      let data = ret.data;
      if (data != null && data.faceInfoMetas != null) {
        let retDat = [];
        for (let fInf of data.faceInfoMetas) {
          let faceUrl = {};
          let faceId = fInf.faceId;
          retDat.push({ fInf, faceUrl, faceId });
        }
        let faceResult = { nextTime: nextTime, faceList: retDat };
        return faceResult;
      }
      throw "empty data";
    } else {
      LogUtil.logOnAll(TAG, "getAllUnmarkFaces", "empty result", ret);
      throw "empty result";
    }
  }
  static async delFigures(aFigureId) {
    let delParams = {
      did: Device.deviceID,
      model: Device.model,
      figureIds: { ids: aFigureId },
    };
    let ret = await API.instance().post(CloudApi.DelFigures, CameraDomain, delParams);
    if (ret != null && 0 == ret.code) {
      return ret
    }

  }

  static async getFacesCluster() {
    let addParams = {
      did: Device.deviceID,
      model: Device.model,
      ...commonParam
    };
    console.log(TAG, "commentFace find add face to existing");
    let ret = await Service.callSmartHomeCameraAPIWithStringParam('/miot/camera/app/v1/get/facesCluster', CameraDomain, false, JSON.stringify(addParams));
    console.log("facesCluster",ret)
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      // console.log("data====", JSON.stringify(data));
      if (data != null && data.clusteringInfos != null) {
        let retDat = [];
        for (let fInf of data.clusteringInfos) {
          let faceUrl = {};
          let updateTime = dayjs(fInf.updateTime).format(LocalizedStrings["yyyymmdd"] + ' HH:mm:ss');
          let faceLable = fInf.faceLable
          let figureName = fInf.figureName ? fInf.figureName : false
          try {
            faceUrl = "";
            // faceUrl = await this.getFaceImgUrl(fInf.faceId.toString());
            // console.log(TAG, "get face:", name, "with", faceUrl);
          } catch (aExp) {
            console.log(TAG, "get faceUrl failed with", fInf.faceId);
            LogUtil.logOnAll(TAG, "getFacesCluster", "get faceUrl failed with", fInf.faceId, aExp);
          }

          retDat.push({ faceLable, updateTime,faceUrl, figureName, faceId: fInf.faceId, faceIds:[], figureId: fInf.figureId });
        }
        return retDat;
      }
    } else {
      LogUtil.logOnAll(TAG, "getFacesCluster", "empty result", ret);
      throw "empty result";
    }
    return ret;

  }
  static async getFaceClusterEvent(faceIds =[], figureId, faceInfo) {
    if (faceIds == null || faceIds.length <= 0) {
      return [];
    }
    let faceStr = "";
    for (let i = 0; i < faceIds.length; i++) {
      faceStr += faceIds[i].toString();
      if (i != faceIds.length - 1) {
        faceStr += ",";
      }
    }
    console.log("faceIdStr:", faceStr);
    let addParams = {
      did: Device.deviceID,
      model: Device.model,
      figureId: figureId,
      faceIds: faceStr,
    }
    let ret = await Service.callSmartHomeCameraAPIWithStringParam("/common/app/get/faceCluster/eventlist", CameraDomain, true, JSONbig.stringify(addParams));
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      // console.log("getFaceClusterEvent====data",data);
      if (data != null && data.thirdPartPlayUnits != null) {
        let retDat = [];
        for (let fInf of data.thirdPartPlayUnits) {
          let eventCoverUrl = null;
          let createTime = fInf.createTime    // dayjs(fInf.createTime).format('YYYY年M月D日,HH:mm');  //'YYYY-MM-DD HH:mm:ss'
          let fileId = fInf.fileId;
          let faceId = fInf.faceId
          let videoStoreId = fInf.videoStoreId
          let isAlarm = fInf.isAlarm
          let offset = fInf.offset
          let originalEventType = fInf.eventType;
          let eventType = this.getDescWithFaceFrom(fInf.eventType, faceInfo);
          let faceEventIcon = this.getIconFrom(fInf.eventType)
          try {
            eventCoverUrl = null;
            // eventCoverUrl = await Service.miotcamera.getFileIdImage(fInf.imgStoreId.toString());
          } catch (aExp) {
            console.log(TAG, "get faceUrl failed with", fInf.faceId);
          }
          // retDat.push({ faceLable, updateTime, faceUrl, figureName, faceId: fInf.faceId, faceIds:[], figureId: fInf.figureId });
          retDat.push({createTime,eventCoverUrl,eventType,fileId,faceId,faceEventIcon,videoStoreId,isAlarm,offset, originalEventType, imgStoreId: fInf.imgStoreId })
        }
        return retDat;
      }
    } else {
      throw "empty result";
    }
    return ret
  }
  //点击报错
  static async faceMistake(fileId, offset, faceId) {
    let param = {
      fileId,
      offset,
      faceId,
      did: Device.deviceID
    }
    let result = Service.callSmartHomeCameraAPIWithStringParam(CloudApi.FaceMistake, CameraDomain, true, JSONbig.stringify(param));
    console.log("result",result);
    return result;
  }

//  //换人脸文案
//  static getDescWithFaceFrom(aType, aFaceInfo) {
//   let pType = this.getPrimaryType(aType);
//   let ret = EvMap[pType];
//   if (ret != null && ret.des_in_label != null) {
//     if (aFaceInfo.name != false) {
//       return this.fmtStr(ret.des_in_label, aFaceInfo.name);
//     } else {
//       return LocalizedStrings["event_desc_unknown_people"];
//     }
//   }
//   return null;
// }

//换图标
static getIconFrom(type, faceName = null) {
  let pType = this.getPrimaryIcoType(type);
  if (pType && pType.indexOf('Face') != -1) {
    pType = "KnownFace";
  }
  let ret = EvMap[pType];
  if (ret) {
    return ret.icon.small;
  } else {
    return DefaltIc;
  }
}



  //换人脸文案
  static getDescWithFaceFrom(aType, aFaceInfo) {
    let pType = this.getPrimaryType(aType);
    let ret = EvMap[pType];
    if (ret != null && ret.des_in_label != null) {
      if (aFaceInfo.name != false) {
        return this.fmtStr(ret.des_in_label, aFaceInfo.name);
      } else {
        return LocalizedStrings["event_desc_unknown_people"];
      }
    }
  }

  static isLanguageCN() {
    let lan = Host.locale.language;
    if (!lan) {
      return true;
    } else if (lan == "zh" || lan == "zh_tw" || lan == "zh_hk") {
      return true;
    } else {
      return false;
    }
  }
  static async getFaceAllFigure() {
    let delParams = {
      did: Device.deviceID,
      model: Device.model
    };
    let ret = await API.instance().get(CloudApi.QueryAllFigure, CameraDomain, delParams);
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      if (data != null && data.figureInfos != null) {
        let retDat = [];
        for (let fInf of data.figureInfos) {
          let faceUrl = {};
          let name = fInf.figureName;
          let faceId = fInf.coverFaceId;
          retDat.push({ name, faceId, faceUrl });
        }
        return retDat;
      }
    } else {
      LogUtil.logOnAll(TAG, "getFaceAllFigure", "empty result", ret);
      throw "empty result";
    }
    return ret;
  }

  static removeSecond(time) {
    let strs = time.split(':');
    if (strs.length < 3) {
      return time;
    } else {
      return `${ strs[0] }:${ strs[1] }`;
    }
  }

  static addSecond(time) {
    let strs = time.split(':');
    if (strs.length > 2) {
      return time;
    } else {
      return `${ strs[0] }:${ strs[1] }:00`;
    }
  }

  // 将repeat值转为对应的文案显示
  static getRepeatString(repeat) {
    // 20241015更新去除工作日 周末展示
    if (0b00000000 == repeat) {
      return LocalizedStrings['plug_timer_onetime'];
    } else if (0b01111111 == repeat) {
      return LocalizedStrings['plug_timer_everyday'];
    } else {
      let dayList = [LocalizedStrings['sunday1'], LocalizedStrings['monday1'], LocalizedStrings['tuesday1'], LocalizedStrings['wednesday1'],
        LocalizedStrings['thursday1'], LocalizedStrings['friday1'], LocalizedStrings['saturday1']];
      let ret = "";

      // 1111111 从左到右  周六 周五 周四 周三 周二 周一 周日
      console.log("==========+++++=====+++++++++++++++++=",repeat);
      // 展示成周一到周日
      // 从周一开始解析
      let flag = 0b00000010;
      for (let i = 1; i < dayList.length; i++) {
        if ((repeat & flag) != 0) {
          if (ret.length > 0) {
            ret += ", ";
          }
          ret += dayList[i];
        }
        flag = flag << 1;
      }
      // 单独处理周日，放到最后面
      if ((repeat & 0b00000001) != 0) {
        if (ret.length > 0) {
          ret += ", ";
        }
        ret += dayList[0];
      }
      return ret;
    }
  }

  static isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  static containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      } else if (str.match(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/)) {
        return true;
      }
      // else if(this.isTextcommon(str)){
      //   return true
      // }
    }
    return false;
  }

  static replaceEmojis(str, replacement = '□') {
    let length = str.length;
    let indexArr = [];
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        indexArr.push(i);
      }
    }
    if (indexArr.length > 0) {
      for (let i = 0; i < indexArr.length; i++) {
        const strArr = str.split('');
        strArr[indexArr[i]] = replacement;
        str = strArr.join('');
      }
    }
    return str;
  }

  // 区分IOS大小机型 812为iphone12mini的逻辑分辨率
  static isHeightPt() {
    let winHeight = Dimensions.get('window').height;
    if (winHeight < 812) {
      return false
    }
    return true
  }

  // 部分页面需要获取iphonex的底部padding。
  static getBottomMarginWithoutSafeArea() {
    let host = NativeModules.MHPluginSDK;
    if (Platform.OS === 'ios' && host.isIphoneXSeries) {
      return 0;
    } else {
      return 20;
    }
  }
}
